{"name": "vue-element-plus-admin", "version": "2.5.6", "description": "一套基于vue3、element-plus、typesScript、vite4的后台集成方案。", "author": "Archer <<EMAIL>>", "private": false, "scripts": {"i": "pnpm install", "dev": "pnpm vite --mode base", "ts:check": "pnpm vue-tsc --noEmit --skipLib<PERSON><PERSON>ck", "build:pro": "pnpm vite build --mode pro", "build:dev": "pnpm vite build --mode base", "build:test": "pnpm vite build --mode test", "serve:pro": "pnpm vite preview --mode pro", "serve:dev": "pnpm vite preview --mode base", "serve:test": "pnpm vite preview --mode test", "npm:check": "pnpx npm-check-updates -u", "clean": "pnpx rimraf node_modules", "clean:cache": "pnpx rimraf node_modules/.cache", "lint:eslint": "eslint --fix --ext .js,.ts,.vue ./src", "lint:format": "prettier --write --loglevel warn \"src/**/*.{js,ts,json,tsx,css,less,vue,html,md}\"", "lint:style": "stylelint --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "prepare": "husky install", "p": "plop"}, "dependencies": {"@faker-js/faker": "^8.4.0", "@iconify/iconify": "^3.1.1", "@iconify/vue": "^4.1.1", "@vueuse/core": "^10.7.2", "@wangeditor/editor": "^5.1.23", "@wangeditor/editor-for-vue": "^5.1.10", "@zxcvbn-ts/core": "^3.0.4", "animate.css": "^4.1.1", "axios": "^1.6.7", "cropperjs": "^1.6.1", "crypto-js": "^4.2.0", "dayjs": "^1.11.10", "driver.js": "^1.3.1", "echarts": "^5.5.1", "echarts-wordcloud": "^2.1.0", "element-plus": "2.8.8", "file-saver": "^2.0.5", "lodash-es": "^4.17.21", "mitt": "^3.0.1", "moment": "^2.30.1", "nprogress": "^0.2.0", "pinia": "^2.1.7", "pinia-plugin-persistedstate": "^3.2.1", "print-js": "^1.6.0", "qrcode": "^1.5.3", "qs": "^6.11.2", "url": "^0.11.3", "vue": "3.4.15", "vue-draggable-plus": "^0.3.5", "vue-i18n": "9.9.1", "vue-json-pretty": "^2.3.0", "vue-router": "^4.2.5", "vue-types": "^5.1.1", "xgplayer": "^3.0.12", "xlsx": "^0.18.5"}, "devDependencies": {"@commitlint/cli": "^18.6.0", "@commitlint/config-conventional": "^18.6.0", "@iconify/json": "^2.2.180", "@intlify/unplugin-vue-i18n": "^2.0.0", "@types/crypto-js": "^4.2.2", "@types/fs-extra": "^11.0.4", "@types/inquirer": "^9.0.7", "@types/lodash-es": "^4.17.12", "@types/node": "^20.11.16", "@types/nprogress": "^0.2.3", "@types/qrcode": "^1.5.5", "@types/qs": "^6.9.11", "@types/sortablejs": "^1.15.7", "@typescript-eslint/eslint-plugin": "^6.21.0", "@typescript-eslint/parser": "^6.21.0", "@unocss/transformer-variant-group": "^0.58.5", "@vitejs/plugin-legacy": "^5.3.0", "@vitejs/plugin-vue": "^5.0.3", "@vitejs/plugin-vue-jsx": "^3.1.0", "autoprefixer": "^10.4.17", "chalk": "^5.3.0", "consola": "^3.2.3", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-define-config": "^2.1.0", "eslint-plugin-prettier": "^5.1.3", "eslint-plugin-vue": "^9.21.1", "esno": "^4.0.0", "fs-extra": "^11.2.0", "husky": "^9.0.10", "inquirer": "^9.2.14", "less": "^4.2.0", "lint-staged": "^15.2.2", "mockjs": "^1.1.0", "plop": "^4.0.1", "postcss": "^8.4.34", "postcss-html": "^1.6.0", "postcss-less": "^6.0.0", "prettier": "^3.2.5", "rimraf": "^5.0.5", "rollup": "^4.9.6", "rollup-plugin-visualizer": "^5.12.0", "stylelint": "^16.2.1", "stylelint-config-html": "^1.1.0", "stylelint-config-recommended": "^14.0.0", "stylelint-config-standard": "^36.0.0", "stylelint-order": "^6.0.4", "terser": "^5.27.0", "typescript": "5.3.3", "unocss": "^0.58.5", "vite": "5.0.12", "vite-plugin-ejs": "^1.7.0", "vite-plugin-eslint": "^1.8.1", "vite-plugin-mock": "2.9.6", "vite-plugin-progress": "^0.0.7", "vite-plugin-purge-icons": "^0.10.0", "vite-plugin-style-import": "2.0.0", "vite-plugin-svg-icons": "^2.0.1", "vue-tsc": "^1.8.27"}, "packageManager": "pnpm@8.1.0", "engines": {"node": ">=18.0.0", "pnpm": ">=8.1.0"}, "license": "MIT", "repository": {"type": "git", "url": "git+https://github.com/kailong321200875/vue-element-plus-admin.git"}, "bugs": {"url": "https://github.com/kailong321200875/vue-element-plus-admin/issues"}, "homepage": "https://github.com/kailong321200875/vue-element-plus-admin"}