import type { RouteRecordRaw } from 'vue-router'
import { defineComponent } from 'vue'

/**
* redirect: noredirect        当设置 noredirect 的时候该路由在面包屑导航中不可被点击
* name:'router-name'          设定路由的名字，一定要填写不然使用<keep-alive>时会出现各种问题
* meta : {
    hidden: true              当设置 true 的时候该路由不会再侧边栏出现 如404，login等页面(默认 false)

    alwaysShow: true          当你一个路由下面的 children 声明的路由大于1个时，自动会变成嵌套的模式，
                              只有一个时，会将那个子路由当做根路由显示在侧边栏，
                              若你想不管路由下面的 children 声明的个数都显示你的根路由，
                              你可以设置 alwaysShow: true，这样它就会忽略之前定义的规则，
                              一直显示根路由(默认 false)

    title: 'title'            设置该路由在侧边栏和面包屑中展示的名字

    icon: 'svg-name'          设置该路由的图标

    noCache: true             如果设置为true，则不会被 <keep-alive> 缓存(默认 false)

    breadcrumb: false         如果设置为false，则不会在breadcrumb面包屑中显示(默认 true)

    affix: true               如果设置为true，则会一直固定在tag项中(默认 false)

    noTagsView: true          如果设置为true，则不会出现在tag中(默认 false)

    activeMenu: '/dashboard'  显示高亮的路由路径

    canTo: true               设置为true即使hidden为true，也依然可以进行路由跳转(默认 false)

    permission: ['edit','add', 'delete']    设置该路由的权限

    menuType: '0' | '1'       设置该路由的类型 0:菜单 1:按钮
  }
**/
declare module 'vue-router' {
  interface RouteMeta extends Record<string | number | symbol, unknown> {
    hidden?: boolean
    alwaysShow?: boolean
    title?: string
    icon?: string
    noCache?: boolean
    breadcrumb?: boolean
    affix?: boolean
    activeMenu?: string
    noTagsView?: boolean
    followAuth?: string
    canTo?: boolean
    permission?: string
    menuType?: '0' | '1'
  }
}

type Component<T = any> =
  | ReturnType<typeof defineComponent>
  | (() => Promise<typeof import('*.vue')>)
  | (() => Promise<T>)

declare global {
  declare interface MenuRouteRaw {
    name?: string // 名称
    path: string // 路由
    weight?: number | string // 排序
    visible?: boolean // 显示
    icon?: boolean // icon
    children?: CustomRouteRaw[] // 子路由
    menuType?: '0' | '1' // 类型 0:菜单 1:按钮
    permission?: string // 权限标识
  }

  declare interface CustomRouteRaw extends Omit<RouteRecordRaw, 'meta'> {
    name?: string // 名称
    path: string // 路由
    fullPath?: string // 完整路径
    identification?: string // 权限标识
    sort?: number | string // 排序
    cache?: boolean // 缓存
    show?: boolean // 显示
    visible?: boolean // 显示
    weight?: number // 排序
    children?: CustomRouteRaw[] // 子路由
    type?: 'menu' | 'button' // 类型
    icon?: string // 图标
  }
  declare interface AppRouteRecordRaw extends Omit<RouteRecordRaw, 'meta'> {
    name: string
    meta: RouteMeta
    sort?: number
    component?: Component | string
    children?: AppRouteRecordRaw[]
    props?: Recordable
    fullPath?: string
  }

  declare interface AppCustomRouteRecordRaw extends Omit<RouteRecordRaw, 'meta'> {
    name: string
    meta: RouteMeta
    component: string
    path: string
    redirect: string
    children?: AppCustomRouteRecordRaw[]
  }
}
