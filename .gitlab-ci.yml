stages:           
  - build
  - release
build-job:
  image: node:18-alpine         
  stage: build
  tags:
    - nodejs-dock
  before_script:
    # - npm config set registry https://registry.npmmirror.com
    # - npm config set proxy http://***********:7890
    # - npm config set https-proxy http://***********:7890
    - npm config set registry https://registry.npmmirror.com
    # - npm i --include=dev --cache .npm --prefer-offline
    - npm install -g pnpm@8.1.0
    - pnpm install --no-frozen-lockfile
  script:
    - pnpm run build:pro
    - ls $CI_PROJECT_DIR
  artifacts:
    name: package
    paths:
      - dist
release-image:
  image: docker:26.1
  stage: release
  dependencies:
    - build-job
  services:
    - docker:26.1.4-dind
  variables:
    DOCKER_TLS_CERTDIR: "/certs"
    CONTAINER_RELEASE_IMAGE: $CI_REGISTRY_IMAGE:latest
  script:
    - ls -l .
    - source ./version
    # - docker login -u $CI_REGISTRY_USER -p $CI_REGISTRY_PASSWORD $CI_REGISTRY
    - docker login -u xujx -p $UBOS_ROOT_PASS $CI_REGISTRY
    - docker run --rm --privileged  --network zyws-net multiarch/qemu-user-static --reset -p yes
    - docker buildx build --platform=linux/amd64,linux/arm64 -t $CI_REGISTRY_IMAGE:$VERSION -t $CONTAINER_RELEASE_IMAGE --push .
    - docker logout
    - docker rmi -f $CI_REGISTRY_IMAGE:$VERSION $CONTAINER_RELEASE_IMAGE
    - docker images -f dangling=true -q | xargs --no-run-if-empty docker rmi
    - docker images
    - rm -rf ./dist/

  only:
    - main
    - edit_config
  except:
    - tags

