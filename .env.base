# 环境
VITE_NODE_ENV=development

# 加密密钥
VITE_ENCRYPT_KEY=6YpbBcJhSiLeiUEX

# 接口前缀
VITE_API_BASE_PATH=/api

# 接口代理地址
VITE_PROXY_TARGET=http://127.0.0.1:3001
# VITE_PROXY_TARGET=http://192.168.19.41:8001
# VITE_PROXY_TARGET=https://fswsjgbackend.jkqy.cn
# 
# 打包路径
VITE_BASE_PATH=/

# 标题
VITE_APP_TITLE=档案库管理

# 是否全量引入element-plus样式
VITE_USE_ALL_ELEMENT_PLUS_STYLE=true

# 是否开启mock
VITE_USE_MOCK=true

# 是否使用在线图标
VITE_USE_ONLINE_ICON=true

# 401页面重定向路径
VITE_REDIRECT_401_PATH=/login