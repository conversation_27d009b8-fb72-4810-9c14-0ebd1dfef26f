import router from './router'
// import { useAppStoreWithOut } from '@/store/modules/app'
import type { RouteRecordRaw } from 'vue-router'
import { useTitle } from '@/hooks/web/useTitle'
import { useNProgress } from '@/hooks/web/useNProgress'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { usePageLoading } from '@/hooks/web/usePageLoading'
import { NO_REDIRECT_WHITE_LIST } from '@/constants'
import { useUserStoreWithOut } from '@/store/modules/user'
// import { getMenuApi,getUserInfoApi } from '@/api/login'
import { authenticateApi } from '@/api/login'
import { ElMessage } from 'element-plus'

const { start, done } = useNProgress()

const { loadStart, loadDone } = usePageLoading()

// const appStore = useAppStoreWithOut()

// 获取角色路由
const getRole = async () => {
  const permissionStore = usePermissionStoreWithOut()
  const userStore = useUserStoreWithOut()

  // const getUserInfoApiRes = await getUserInfoApi()
  // if (getUserInfoApiRes.success) {
  //   permissionStore.setPermissionList(getUserInfoApiRes.data.permissions)
  // }
  // // 如果 administerArea（字符串）和 roles（数组）都为空，弹出提示
  // if (!getUserInfoApiRes.data.administerArea && !getUserInfoApiRes.data.roles.length) {
  //   ElMessage.error('您尚未设置单位和角色信息，请先联系超级管理员进行配置，完成后再尝试登录系统。')
  //   // return
  // }

  // const res = await getMenuApi()
  // if (res) {
  //   const routers = res.data || []
  //   userStore.setRoleRouters(routers) // 设置角色路由
  //   await permissionStore.generateRoutes(routers).catch(() => {})
  //   permissionStore.getAddRouters.forEach((route) => {
  //     router.addRoute(route as RouteRecordRaw) // 动态添加可访问路由表
  //   })
  //   permissionStore.setIsAddRouters(true)
  //   router.push(permissionStore.addRouters[0].path || '/')
  // }
  // const res = await getMenuApi()
  // if (res) {
  // const routers = res.data || []
  const routers = []
  userStore.setRoleRouters(routers) // 设置角色路由
  await permissionStore.generateRoutes(routers).catch(() => {})
  permissionStore.getAddRouters.forEach((route) => {
    router.addRoute(route as RouteRecordRaw) // 动态添加可访问路由表
  })
  permissionStore.setIsAddRouters(true)
  // }
  router.push('/')
}

router.beforeEach(async (to, from, next) => {
  start()
  loadStart()

  const permissionStore = usePermissionStoreWithOut()
  const userStore = useUserStoreWithOut()

  // 如果有code参数，就去请求token
  const query = to.query
  if (query.code) {
    try {
      const res = await authenticateApi({ code: query.code as string })
      if (res.code === 0) {
        const token = res.data.token
        if (typeof sensors_sw !== 'undefined') {
          sensors_sw.login('用户标识')
        }
        userStore.setToken(token)
        // 去除url中的code参数
        const newQuery = { ...to.query, code: undefined }

        if (to.path === '/login') {
          next({ path: '/', query: newQuery })
        } else {
          await getRole()
          if (permissionStore.getIsAddRouters) {
            next()
            return
          }

          // 开发者可根据实际情况进行修改
          const roleRouters = userStore.getRoleRouters || []

          //  动态路由
          await permissionStore.generateRoutes(roleRouters as CustomRouteRaw[])

          permissionStore.getAddRouters.forEach((route) => {
            router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
          })
          const redirectPath = from.query.redirect || to.path
          const redirect = decodeURIComponent(redirectPath as string)
          const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
          permissionStore.setIsAddRouters(true)
          next({ ...nextData, query: newQuery })
        }
      } else {
        ElMessage.error(res.msg)
        throw new Error(res.msg)
      }
    } catch (error) {
      next('/uncertified')
      return
    }
  } else if (userStore.getToken) {
    if (to.path === '/login') {
      next({ path: permissionStore.getAddRouters[0].path })
    } else {
      if (permissionStore.getIsAddRouters) {
        next()
        return
      }

      // 开发者可根据实际情况进行修改
      const roleRouters = userStore.getRoleRouters || []

      // 使用动态路由
      await permissionStore.generateRoutes(roleRouters as CustomRouteRaw[])

      permissionStore.getAddRouters.forEach((route) => {
        router.addRoute(route as unknown as RouteRecordRaw) // 动态添加可访问路由表
      })
      const redirectPath = from.query.redirect || to.path
      const redirect = decodeURIComponent(redirectPath as string)
      const nextData = to.path === redirect ? { ...to, replace: true } : { path: redirect }
      permissionStore.setIsAddRouters(true)
      next(nextData)
    }
  } else {
    if (NO_REDIRECT_WHITE_LIST.indexOf(to.path) !== -1) {
      next()
    } else {
      console.log('无token全部重定向到uncertified')
      next(`/uncertified`)
    }
  }
})

router.afterEach((to) => {
  if (typeof sensors_sw !== 'undefined') {
    sensors_sw.quick('autoTrackSinglePage') // SDK API
  }
  useTitle(to?.meta?.title as string)
  done() // 结束Progress
  loadDone()
})
