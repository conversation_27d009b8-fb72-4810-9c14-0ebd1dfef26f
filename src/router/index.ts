import { createRouter, createWebHistory } from 'vue-router'
import type { RouteRecordRaw } from 'vue-router'
import type { App } from 'vue'
import { Layout } from '@/utils/routerHelper'
import { useI18n } from '@/hooks/web/useI18n'
// import { getParentLayout } from '@/utils/routerHelper' // 如果使用了多级菜单，需要引入并使用

const { t } = useI18n()

export const constantRouterMap: AppRouteRecordRaw[] = [
  // {
  //   path: '/',
  //   component: Layout,
  //   // redirect: '', // 重定向 根据返回的动态路由设置 permission.ts -> generateRoutes
  //   name: 'Root',
  //   meta: {
  //     hidden: true
  //   }
  // },
  // {
  //   path: '/information',
  //   component: Layout,
  //   redirect: '/information',
  //   name: 'Information',
  //   meta: {},
  //   children: [
  //     {
  //       path: 'information',
  //       component: () => import('@/views/information/information.vue'),
  //       name: 'Information',
  //       meta: {
  //         title: '个人信息查询与维护'
  //       }
  //     }
  //   ]
  // },
  // 项目管理
  // {
  //   path: '/projectManagement',
  //   component: Layout,
  //   redirect: '/projectManagement/putRecord',
  //   name: 'ProjectManagement',
  //   meta: {
  //     title: '项目管理',
  //     alwaysShow: true,
  //     menuLevel: 1
  //   },
  //   children: [
  //     {
  //       path: 'putRecord',
  //       component: () => import('@/views/projectManagement/putRecord/putRecord.vue'),
  //       name: 'PutRecord',
  //       meta: {
  //         title: '立项管理'
  //       }
  //     },
  //     {
  //       path: 'schemeApplication',
  //       component: () =>
  //         import('@/views/projectManagement/schemeApplication/schemeApplication.vue'),
  //       name: 'SchemeApplication',
  //       meta: {
  //         title: '方案申报'
  //       }
  //     },
  //     {
  //       path: 'schemeApproval',
  //       component: () => import('@/views/projectManagement/schemeApproval/schemeApproval.vue'),
  //       name: 'SchemeApproval',
  //       meta: {
  //         title: '方案审批'
  //       }
  //     },
  //     {
  //       path: 'projectProgressReport',
  //       component: () =>
  //         import('@/views/projectManagement/projectProgressReport/projectProgressReport.vue'),
  //       name: 'ProjectProgressReport',
  //       meta: {
  //         title: '项目进度汇报'
  //       }
  //     },
  //     {
  //       path: 'projectPeriodicReport',
  //       component: () =>
  //         import('@/views/projectManagement/projectPeriodicReport/projectPeriodicReport.vue'),
  //       name: 'ProjectPeriodicReport',
  //       meta: {
  //         title: '项目阶段性汇报'
  //       }
  //     },
  //     {
  //       path: 'projectInstruction',
  //       component: () =>
  //         import('@/views/projectManagement/projectInstruction/projectInstruction.vue'),
  //       name: 'ProjectInstruction',
  //       meta: {
  //         title: '项目批示管理'
  //       }
  //     },
  //     {
  //       path: 'projectTransaction',
  //       component: () =>
  //         import('@/views/projectManagement/projectTransaction/projectTransaction.vue'),
  //       name: 'ProjectTransaction',
  //       meta: {
  //         title: '项目异动管理'
  //       }
  //     },
  //     {
  //       path: 'completionDeclaration',
  //       component: () =>
  //         import('@/views/projectManagement/completionDeclaration/completionDeclaration.vue'),
  //       name: 'CompletionDeclaration',
  //       meta: {
  //         title: '项目完成申报'
  //       }
  //     },
  //     {
  //       path: 'completionApproval',
  //       component: () =>
  //         import('@/views/projectManagement/completionApproval/completionApproval.vue'),
  //       name: 'CompletionApproval',
  //       meta: {
  //         title: '项目完成审批'
  //       }
  //     },
  //     {
  //       path: 'projectEntriesFiled',
  //       component: () =>
  //         import('@/views/projectManagement/projectEntriesFiled/projectEntriesFiled.vue'),
  //       name: 'ProjectEntriesFiled',
  //       meta: {
  //         title: '项目结项归档'
  //       }
  //     },
  //     {
  //       path: 'budgetPerformanceRecords',
  //       component: () =>
  //         import('@/views/projectManagement/budgetPerformanceRecords/budgetPerformanceRecords.vue'),
  //       name: 'BudgetPerformanceRecords',
  //       meta: {
  //         title: '项目预算执行情况记录'
  //       }
  //     }
  //   ]
  // },
  {
    path: '/redirect',
    component: Layout,
    name: 'RedirectWrapper',
    children: [
      {
        path: '/redirect/:path(.*)',
        name: 'Redirect',
        component: () => import('@/views/Redirect/Redirect.vue'),
        meta: {}
      }
    ],
    meta: {
      hidden: true,
      noTagsView: true
    }
  },
  {
    path: '/login',
    component: () => import('@/views/Login/Login.vue'),
    name: 'Login',
    meta: {
      hidden: true,
      title: t('router.login'),
      noTagsView: true
    }
  },
  {
    path: '/personal',
    component: Layout,
    redirect: '/personal/personal-center',
    name: 'Personal',
    meta: {
      title: t('router.personal'),
      hidden: true,
      canTo: true
    },
    children: [
      {
        path: 'personal-center',
        component: () => import('@/views/Personal/PersonalCenter/PersonalCenter.vue'),
        name: 'PersonalCenter',
        meta: {
          title: t('router.personalCenter'),
          hidden: true,
          canTo: true
        }
      }
    ]
  },
  {
    path: '/404',
    component: () => import('@/views/ErrorComponent/404.vue'),
    name: 'NoFind',
    meta: {
      hidden: true,
      title: '404',
      noTagsView: true
    }
  },
  {
    path: '/uncertified',
    component: () => import('@/views/ErrorComponent/Uncertified.vue'),
    name: 'Uncertified',
    meta: {
      hidden: true,
      title: 'uncertified',
      noTagsView: true
    }
  }
]

// 不会出现在菜单中的按钮及路由请加上hidden: true和canTo: true，确保不会出现在菜单中，但可以通过路由跳转
// 一级菜单如果请加上 alwaysShow: true, 确保一级菜单只有一个子菜单时也会显示
export const asyncRouterMap: AppRouteRecordRaw[] = []

const router = createRouter({
  history: createWebHistory(import.meta.env.VITE_BASE_PATH),
  strict: true,
  routes: constantRouterMap as RouteRecordRaw[],
  scrollBehavior: () => ({ left: 0, top: 0 })
})

export const resetRouter = (): void => {
  const resetWhiteNameList = ['Redirect', 'Login', 'NoFind', 'Root', 'Uncertified']
  router.getRoutes().forEach((route) => {
    const { name } = route
    if (name && !resetWhiteNameList.includes(name as string)) {
      router.hasRoute(name) && router.removeRoute(name)
    }
  })
}

export const setupRouter = (app: App<Element>) => {
  app.use(router)
}

export default router
