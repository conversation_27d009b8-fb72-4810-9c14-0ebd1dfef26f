import CryptoJS from 'crypto-js'
const VITE_ENCRYPT_KEY = import.meta.env.VITE_ENCRYPT_KEY || '6YpbBcJhSiLeiUEX'

export const getEncryptedData = (data: string) => {
  const key = CryptoJS.enc.Utf8.parse(VITE_ENCRYPT_KEY)
  // 生成一个随机的16位向量
  const randomIv = generateRandomNumericString(16)
  const iv = CryptoJS.enc.Utf8.parse(randomIv)
  const encryptedData = CryptoJS.AES.encrypt(data, key, {
    iv: iv,
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7
  })

  const encryptedBase64 = CryptoJS.enc.Base64.stringify(encryptedData.ciphertext)
  return {
    iv: randomIv,
    data: encryptedBase64
  }
}

const generateRandomNumericString = (length) => {
  let result = ''
  const characters = '0123456789'
  const charactersLength = characters.length
  for (let i = 0; i < length; i++) {
    result += characters.charAt(Math.floor(Math.random() * charactersLength))
  }
  return result
}
