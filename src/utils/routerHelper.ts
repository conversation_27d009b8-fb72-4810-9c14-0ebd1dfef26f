import { createRouter, createWebHistory } from 'vue-router'
import type {
  Router,
  RouteLocationNormalized,
  RouteRecordNormalized,
  RouteRecordRaw
} from 'vue-router'
import { isUrl } from '@/utils/is'
import { omit, cloneDeep } from 'lodash-es'

const modules = import.meta.glob('../views/**/*.{vue,tsx}')

import router from '@/router'
import { usePermissionStoreWithOut } from '@/store/modules/permission'
import { useUserStoreWithOut } from '@/store/modules/user'
// import { getMenuApi, getUserInfoApi } from '@/api/login'

// import { ElMessage } from 'element-plus'

export const getRoleRouters = async () => {
  const permissionStore = usePermissionStoreWithOut()
  const userStore = useUserStoreWithOut()

  // const getUserInfoApiRes = await getUserInfoApi()
  // if (getUserInfoApiRes.success) {
  //   permissionStore.setPermissionList(getUserInfoApiRes.data.permissions)
  //   localStorage.setItem('administerArea', getUserInfoApiRes.data.administerArea)
  //   // console.log('🍊permissionStore PermissionList', permissionStore.getPermissionList)
  // }

  // // 如果 administerArea（字符串）和 roles（数组）都为空，弹出提示
  // if (!getUserInfoApiRes.data.administerArea && !getUserInfoApiRes.data.roles.length) {
  //   ElMessage.error('您尚未设置单位和角色信息，请先联系超级管理员进行配置，完成后再尝试登录系统。')
  //   // return
  // }

  // const res = await getMenuApi()
  // if (res) {
  // const routers = res.data || []
  const routers = []
  userStore.setRoleRouters(routers) // 设置角色路由
  await permissionStore.generateRoutes(routers).catch(() => {})
  permissionStore.getAddRouters.forEach((route) => {
    router.addRoute(route as RouteRecordRaw) // 动态添加可访问路由表
  })
  permissionStore.setIsAddRouters(true)
  // }
  router.push('/')
}

/* Layout */
export const Layout = () => import('@/layout/Layout.vue')

export const getParentLayout = () => {
  return () =>
    new Promise((resolve) => {
      resolve({
        name: 'ParentLayout'
      })
    })
}

export const getRawRoute = (route: RouteLocationNormalized): RouteLocationNormalized => {
  if (!route) return route
  const { matched, ...opt } = route
  return {
    ...opt,
    matched: (matched
      ? matched.map((item) => ({
          meta: item.meta,
          name: item.name,
          path: item.path
        }))
      : undefined) as RouteRecordNormalized[]
  }
}

export const generateRoutesByFrontEnd = (
  routes: AppRouteRecordRaw[],
  keys: string[],
  basePath = '/'
): AppRouteRecordRaw[] => {
  const res: AppRouteRecordRaw[] = []

  for (const route of routes) {
    const meta = route.meta ?? {}
    // skip some route
    if (meta.hidden && !meta.canTo) {
      continue
    }

    let data: Nullable<AppRouteRecordRaw> = null

    let onlyOneChild: Nullable<string> = null
    if (route.children && route.children.length === 1 && !meta.alwaysShow) {
      onlyOneChild = (
        isUrl(route.children[0].path)
          ? route.children[0].path
          : pathResolve(pathResolve(basePath, route.path), route.children[0].path)
      ) as string
    }

    // 开发者可以根据实际情况进行扩展
    for (const item of keys) {
      // 通过路径去匹配
      if (isUrl(item) && (onlyOneChild === item || route.path === item)) {
        data = Object.assign({}, route)
      } else {
        const routePath = (onlyOneChild ?? pathResolve(basePath, route.path)).trim()
        if (routePath === item || meta.followRoute === item) {
          data = Object.assign({}, route)
        }
      }
    }

    // recursive child routes
    if (route.children && data) {
      data.children = generateRoutesByFrontEnd(
        route.children,
        keys,
        pathResolve(basePath, data.path)
      )
    }
    if (data) {
      res.push(data as AppRouteRecordRaw)
    }
  }
  return res
}
export const generateRoutesByServer = (routes: AppCustomRouteRecordRaw[]): AppRouteRecordRaw[] => {
  const res: AppRouteRecordRaw[] = []

  for (const route of routes) {
    const data: AppRouteRecordRaw = {
      path: route.path,
      name: route.name,
      redirect: route.redirect,
      meta: route.meta
    }
    if (route.component) {
      const comModule = modules[`../${route.component}.vue`] || modules[`../${route.component}.tsx`]
      const component = route.component as string
      if (!comModule && !component.includes('#')) {
        console.error(`未找到${route.component}.vue文件或${route.component}.tsx文件，请创建`)
      } else {
        // 动态加载路由文件，可根据实际情况进行自定义逻辑
        data.component =
          component === '#' ? Layout : component.includes('##') ? getParentLayout() : comModule
      }
    }
    // recursive child routes
    if (route.children) {
      data.children = generateRoutesByServer(route.children)
    }
    res.push(data as AppRouteRecordRaw)
  }
  return res
}

//
// 动态生成路由
export const dainamicGenerateRoutes = (
  routes: AppRouteRecordRaw[],
  keys: CustomRouteRaw[],
  basePath = '/'
): AppRouteRecordRaw[] => {
  const res: AppRouteRecordRaw[] = []

  // 扁平化，避免性能问题同时简化处理逻辑
  const list: CustomRouteRaw[] = []
  flatRoutes(keys, list) // 扁平化后端返回的路由
  // console.log('flat Route list🍊', list)

  const permissionStore = usePermissionStoreWithOut()
  const permissionList = permissionStore.getPermissionList

  for (const route of routes) {
    // route 前端路由表中的路由 meta：router的meta信息
    const meta = route.meta ?? {}
    // console.log('route🍊', route)

    // 可能将要生成的路由
    let data: Nullable<AppRouteRecordRaw> = null

    // 如果是按钮路由 menuType: '1' 全部设置meta.hidden = true, meta.canTo = true , 并且直接注册路由
    // TODO 通过permission判断，如果没有权限则不生成路由或是canTo = false
    if (meta.menuType === '1') {
      // if (permissionList.includes(meta.permission)) {
      data = Object.assign(route, {
        meta: { hidden: true, canTo: permissionList.includes(meta.permission), ...route.meta }
      })
      // }
    } else {
      // 菜单路由
      // 如果 hidden 为 true 并且 canTo 为 false，则跳过，不生成路由
      if (meta.hidden && !meta.canTo) {
        continue
      }

      for (const item of list) {
        const path = item.path || item.fullPath
        // 通过路径去匹配
        const routePath = pathResolve(basePath, route.path).trim()
        if (routePath === path) {
          const obj: { sort?: number; meta: { hidden: boolean; title?: string; icon?: string } } = {
            meta: { hidden: true, ...route.meta }
          }
          obj.meta.hidden = !item.visible
          obj.sort = item.weight
          obj.meta.title = item.name
          obj.meta.icon = item.icon
          data = Object.assign(route, obj)
          break
        }
      }

      // recursive child routes
      if (route.children && data) {
        data.children = dainamicGenerateRoutes(
          route.children,
          keys,
          pathResolve(basePath, data.path)
        )
      }
    }

    if (data) {
      res.push(data as AppRouteRecordRaw)
    }
  }

  // 这是假的  有真实数据删掉
  const jiaRes = [
    {
      path: '/enterprise',
      component: Layout,
      redirect: '/enterprise/list',
      name: 'Enterprise',
      meta: {
        title: '用人单位“一企一档”查阅',
        alwaysShow: true,
        menuLevel: 1
      },
      children: [
        {
          path: 'list',
          component: () => import('@/views/enterprise/list/index.vue'),
          name: 'EnterpriseList',
          meta: {
            title: '企业列表'
          }
        },
        {
          path: 'detail',
          component: () => import('@/views/enterprise/detail/index.vue'),
          name: 'EnterpriseDetail',
          meta: {
            title: '企业详情',
            hidden: true
          }
        }
      ]
    },
    {
      path: '/employee',
      component: Layout,
      redirect: '/employee/list',
      name: 'Employee',
      meta: {
        title: '劳动者“一人一档”管理',
        alwaysShow: true,
        menuLevel: 1
      },
      children: [
        {
          path: 'list',
          component: () => import('@/views/employee/list/index.vue'),
          name: 'EmployeeList',
          meta: {
            title: '劳动者列表'
          }
        },
        {
          path: 'detail',
          component: () => import('@/views/employee/detail/index.vue'),
          name: 'EmployeeDetail',
          meta: {
            title: '劳动者详情',
            hidden: true
          }
        },
        {
          path: 'infoSet',
          component: () => import('@/views/employee/infoSet/index.vue'),
          name: 'InfoSet',
          meta: {
            title: '信息集与栏目管理'
          }
        },
        {
          path: 'category',
          component: () => import('@/views/employee/category/category.vue'),
          name: 'Category',
          meta: {
            title: '劳动者类别管理'
          }
        },
        {
          path: 'dataReport',
          component: () => import('@/views/employee/dataReport/index.vue'),
          name: 'dataReport',
          meta: {
            title: '数据上报权设置'
          }
        },
        {
          path: 'dataApproval',
          component: () => import('@/views/employee/dataApproval/index.vue'),
          name: 'dataApproval',
          meta: {
            title: '数据审批权设置'
          }
        }
      ]
    },
    {
      path: '/organization',
      component: Layout,
      redirect: '/organization/list',
      name: 'Organization',
      meta: {
        title: '技术支撑机构管理',
        alwaysShow: true,
        menuLevel: 1
      },
      children: [
        {
          path: 'list',
          component: () => import('@/views/organization/list/index.vue'),
          name: 'OrganizationList',
          meta: {
            title: '机构列表'
          }
        },
        {
          path: 'detail',
          component: () => import('@/views/organization/detail/index.vue'),
          name: 'OrganizationDetail',
          meta: {
            title: '机构详情',
            hidden: true
          }
        }
      ]
    }
  ]
  // 对路由进行排序，包括子路由
  // sortRoutes(res)
  // return res
  return jiaRes // 这是假的  有真实数据删掉
}

// 多维数组转一维数组
const flatRoutes = (routes: CustomRouteRaw[], res: CustomRouteRaw[]) => {
  for (let i = 0; i < routes.length; i++) {
    const item = routes[i]
    item.path = pathResolve('', item.path)
    if (item.path) {
      res.push(item)
      if (item.children) {
        flatRoutes(item.children, res)
      }
    }
  }
}

const sortRoutes = (routes: AppRouteRecordRaw[]) => {
  routes.sort((a, b) => {
    return (a.sort || 0) - (b.sort || 0)
  })
  routes.forEach((route) => {
    if (route.children) {
      sortRoutes(route.children)
    }
  })
}

export const pathResolve = (parentPath: string, path: string) => {
  // if (isUrl(path)) return path
  let childPath = path.startsWith('/') || !path ? path : `/${path}`
  // 截去 /: 后面的参数
  const index = childPath.indexOf('/:')
  if (index !== -1) {
    childPath = childPath.slice(0, index)
  }
  return `${parentPath}${childPath}`.replace(/\/\//g, '/').trim()
}

// 路由降级
export const flatMultiLevelRoutes = (routes: AppRouteRecordRaw[]) => {
  const modules: AppRouteRecordRaw[] = cloneDeep(routes)
  for (let index = 0; index < modules.length; index++) {
    const route = modules[index]
    if (!isMultipleRoute(route)) {
      continue
    }
    promoteRouteLevel(route)
  }
  return modules
}

// 层级是否大于2
const isMultipleRoute = (route: AppRouteRecordRaw) => {
  if (!route || !Reflect.has(route, 'children') || !route.children?.length) {
    return false
  }

  const children = route.children

  let flag = false
  for (let index = 0; index < children.length; index++) {
    const child = children[index]
    if (child.children?.length) {
      flag = true
      break
    }
  }
  return flag
}

// 生成二级路由
const promoteRouteLevel = (route: AppRouteRecordRaw) => {
  let router: Router | null = createRouter({
    routes: [route as RouteRecordRaw],
    history: createWebHistory()
  })

  const routes = router.getRoutes()
  addToChildren(routes, route.children || [], route)
  router = null

  route.children = route.children?.map((item) => omit(item, 'children'))
}

// 添加所有子菜单
const addToChildren = (
  routes: RouteRecordNormalized[],
  children: AppRouteRecordRaw[],
  routeModule: AppRouteRecordRaw
) => {
  for (let index = 0; index < children.length; index++) {
    const child = children[index]
    const route = routes.find((item) => item.name === child.name)
    if (!route) {
      continue
    }
    routeModule.children = routeModule.children || []
    if (!routeModule.children.find((item) => item.name === route.name)) {
      routeModule.children?.push(route as unknown as AppRouteRecordRaw)
    }
    if (child.children?.length) {
      addToChildren(routes, child.children, routeModule)
    }
  }
}
