import type { App } from 'vue'
import { usePermissionStoreWithOut } from '@/store/modules/permission'

// 不推荐在组件上使用自定义指令。当组件具有多个根节点时可能会出现预期外的行为。
// 组件可能含有多个根节点。当应用到一个多根组件时，指令将会被忽略且抛出一个警告。
// 如果需要在组件上使用指令，需要确保组件只有一个根节点。或者可以使用div包裹组件并绑定在div上。

export const setUpDirective = (app: App<Element>) => {
  // 注册一个全局自定义指令 `v-permissionIf`
  // 作用类似于 v-if，用于控制权限
  app.directive('permissionIf', {
    mounted(el, binding) {
      const permissionStore = usePermissionStoreWithOut()
      const { value } = binding // 获取到 v-permission的值
      const permissionList = permissionStore.getPermissionList
      const hasPermission = permissionList.includes(value) // 判断是否有权限
      if (!hasPermission) {
        // 没有权限则移除元素
        el.parentNode && el.parentNode.removeChild(el)
      }
    }
  })

  // `v-permissionShow`
  // 作用类似于 v-show，用于控制权限
  app.directive('permissionShow', {
    mounted(el, binding) {
      const permissionStore = usePermissionStoreWithOut()
      const { value } = binding // 获取到 v-permission的值
      const permissionList = permissionStore.getPermissionList
      const hasPermission = permissionList.includes(value) // 判断是否有权限
      if (!hasPermission) {
        // 没有权限则隐藏元素
        el.style.display = 'none'
      }
    }
  })
}
