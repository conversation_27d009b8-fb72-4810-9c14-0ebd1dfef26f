/**
 * 请求成功状态码
 */
export const SUCCESS_CODE = 200

/**
 * 请求失败状态码
 */
export const ERROT_CODE = -1

/**
 * 登录状态过期
 */
export const TOKEN_EXPIRED = 401

/**
 * 请求contentType
 */
export const CONTENT_TYPE: AxiosContentType = 'application/json'

/**
 * 请求超时时间
 */
export const REQUEST_TIMEOUT = 60000

/**
 * 不重定向白名单
 */
export const NO_REDIRECT_WHITE_LIST = [
  '/login',
  '/uncertified',
  '/404',
  '/register',
  '/employerLogin'
] // 生产模式不需要login请删除

/**
 * 不重置路由白名单
 */
export const NO_RESET_WHITE_LIST = [
  'Redirect',
  'Login',
  'NoFind',
  'Root',
  'Uncertified',
  'Register',
  'EmployerLogin'
]

/**
 * 表格默认过滤列设置字段
 */
export const DEFAULT_FILTER_COLUMN = ['expand', 'selection']

/**
 * 是否根据headers->content-type自动转换数据格式
 */
export const TRANSFORM_REQUEST_DATA = true
