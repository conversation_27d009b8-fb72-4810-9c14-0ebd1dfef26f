<template>
  <el-form ref="elFormRef" :model="formData" :rules="rules" label-width="140px">
    <el-form-item prop="oldMobile" label="旧手机号码">
      <el-input v-model="formData.oldMobile" />
    </el-form-item>
    <el-form-item prop="oldCode" label="旧手机验证码">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input v-model="formData.oldCode" />
        </el-col>
        <el-col :span="16">
          <el-button type="primary" @click="getCodeOld" :disabled="oldCodeText != '获取验证码'">
            {{ oldCodeText }}
          </el-button>
        </el-col>
      </el-row>
    </el-form-item>
    <el-form-item prop="newMobile" label="新手机号码">
      <el-input v-model="formData.newMobile" />
    </el-form-item>
    <el-form-item prop="newCode" label="新手机验证码">
      <el-row :gutter="20">
        <el-col :span="8">
          <el-input v-model="formData.newCode" />
        </el-col>
        <el-col :span="16">
          <el-button type="primary" @click="getCodeNew" :disabled="newCodeText != '获取验证码'">
            {{ newCodeText }}
          </el-button>
        </el-col>
      </el-row>
    </el-form-item>
  </el-form>
  <ElDivider />
  <BaseButton type="primary" @click="save">确认修改</BaseButton>
</template>

<script lang="ts" setup>
import { ref, watch } from 'vue'
import {
  ElDivider,
  ElMessage,
  // ElMessageBox,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElRow,
  ElCol
} from 'element-plus'

const formData = ref({
  oldMobile: '',
  oldCode: '',
  newMobile: '',
  newCode: ''
})

const props = defineProps({
  userInfo: {
    type: Object,
    default: () => ({})
  }
})

watch(
  () => props.userInfo,
  (value) => {
    if (value.mobile) {
      formData.value.oldMobile = value.mobile
    }
  },
  {
    immediate: true,
    deep: true
  }
)

const rules = ref({
  oldMobile: [
    { required: true, message: '请输入旧手机号码', trigger: 'change' },
    { pattern: /^1\d{10}$/, message: '请输入正确的手机号码', trigger: 'change' }
  ],
  newMobile: [
    { required: true, message: '请输入新手机号码', trigger: 'change' },
    { pattern: /^1\d{10}$/, message: '请输入正确的手机号码', trigger: 'change' }
  ],
  oldCode: [{ required: true, message: '请输入新手机号码', trigger: 'change' }],
  newCode: [{ required: true, message: '请输入新手机号验证码', trigger: 'change' }]
})

const oldCodeTimeout = ref<any>(null)
const oldCodeText = ref<number | string>('获取验证码')
const newCodeTimeout = ref<any>(null)
const newCodeText = ref<number | string>('获取验证码')

const getCodeOld = () => {
  if (formData.value.oldMobile.length === 11 && oldCodeText.value === '获取验证码') {
    ElMessage.success('验证码已发送')
    let count = 60
    oldCodeText.value = count
    oldCodeTimeout.value = setTimeout(() => {
      count--
      if (count > 0) {
        oldCodeText.value = count
      } else {
        clearInterval(oldCodeTimeout.value)
        oldCodeText.value = '获取验证码'
      }
    }, 1000)
  }
}

const getCodeNew = () => {
  if (formData.value.newMobile.length === 11 && newCodeText.value === '获取验证码') {
    ElMessage.success('验证码已发送')
    let count = 60
    newCodeText.value = count
    newCodeTimeout.value = setTimeout(() => {
      count--
      if (count > 0) {
        newCodeText.value = count
      } else {
        clearInterval(newCodeTimeout.value)
        newCodeText.value = '获取验证码'
      }
    }, 1000)
  }
}

const elFormRef = ref<InstanceType<typeof ElForm>>()

const save = () => {
  if (!elFormRef.value) return
  elFormRef.value.validate((valid, fields) => {
    if (valid) {
      ElMessage.success('保存成功')
    } else {
      console.log('error submit!', fields)
    }
  })
}
</script>

<style scoped>
:deep(.el-row) {
  width: 100%;
}
</style>
