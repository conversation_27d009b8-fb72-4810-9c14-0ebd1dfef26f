<script setup lang="ts">
import { ContentWrap } from '@/components/ContentWrap'
import { ref, unref, computed } from 'vue'
import { ElDivider, ElImage, ElTag, ElTabPane, ElTabs, ElButton, ElMessage } from 'element-plus'
import defaultAvatar from '@/assets/imgs/defaultAvatar.png'
import UploadAvatar from './components/UploadAvatar.vue'
import { Dialog } from '@/components/Dialog'
import EditInfo from './components/EditInfo.vue'
// import EditMobile from './components/EditMobile.vue'
import EditPassword from './components/EditPassword.vue'
import { getUserInfoApi } from '@/api/user'

const userInfo = ref()
const fetchDetailUserApi = async () => {
  const res = await getUserInfoApi()
  console.log('userinfo🍊', res)

  // 这里可以调用接口获取用户信息
  // const data = {
  //   id: 1,
  //   username: 'admin',
  //   name: '张三',
  //   mobile: '18888888888',
  //   // avatarUrl: '',
  //   orgName:'浙江省疾控预防控制中心',
  //   roleNames: ['超级管理员']
  // }
  userInfo.value = res.data
}
fetchDetailUserApi()

const showEdit = computed(() => {
  // 包含 企业 或者 用人单位 的角色才显示编辑
  let res = false
  for (let item of userInfo.value?.roleNames || []) {
    if (item.includes('企业') || item.includes('用人单位')) {
      res = true
      break
    }
  }
  return res
})

const activeName = ref('first')

const dialogVisible = ref(false)

const uploadAvatarRef = ref<ComponentRef<typeof UploadAvatar>>()
const avatarLoading = ref(false)
const saveAvatar = async () => {
  try {
    avatarLoading.value = true
    const base64 = unref(uploadAvatarRef)?.getBase64()
    console.log(base64)
    // 这里可以调用修改头像接口
    fetchDetailUserApi()
    ElMessage.success('修改成功')
    dialogVisible.value = false
  } catch (error) {
    console.log(error)
  } finally {
    avatarLoading.value = false
  }
}
</script>

<template>
  <div class="flex w-100% h-100%">
    <ContentWrap title="个人信息" class="w-400px">
      <div class="flex justify-center items-center">
        <div
          class="avatar w-[150px] h-[150px] relative cursor-pointer"
          @click="dialogVisible = true"
        >
          <ElImage
            class="w-[150px] h-[150px] rounded-full"
            :src="userInfo?.avatarUrl || defaultAvatar"
            fit="fill"
          />
        </div>
      </div>
      <ElDivider />
      <div class="flex justify-between items-center">
        <div>用户名：</div>
        <div>{{ userInfo?.username }}</div>
      </div>
      <ElDivider />
      <div class="flex justify-between items-center">
        <div>姓名：</div>
        <div>{{ userInfo?.name }}</div>
      </div>
      <ElDivider />
      <div class="flex justify-between items-center">
        <div>手机号码：</div>
        <div>{{ userInfo?.mobile ?? '-' }}</div>
      </div>
      <ElDivider />
      <div class="flex justify-between items-center">
        <div>所属单位：</div>
        <div>{{ userInfo?.orgName ?? '-' }}</div>
      </div>
      <ElDivider />
      <div class="flex justify-between items-center">
        <div style="white-space: nowrap">所属角色：</div>
        <div>
          <template v-if="userInfo?.roleNames?.length">
            <ElTag
              style="margin-bottom: 10px"
              v-for="item in userInfo?.roleNames || []"
              :key="item"
              class="ml-2 mb-w"
            >
              {{ item }}
            </ElTag>
          </template>
          <template v-else>-</template>
        </div>
      </div>
      <ElDivider />
    </ContentWrap>
    <ContentWrap v-if="showEdit" title="基本资料" class="flex-[3] ml-20px">
      <ElTabs v-model="activeName">
        <ElTabPane label="基本信息" name="first">
          <EditInfo :user-info="userInfo" @refresh="fetchDetailUserApi" />
        </ElTabPane>
        <ElTabPane label="修改密码" name="second">
          <EditPassword />
        </ElTabPane>
        <!-- <ElTabPane label="修改手机号" name="third">
          <EditMobile :user-info="userInfo" />
        </ElTabPane> -->
      </ElTabs>
    </ContentWrap>
  </div>

  <Dialog v-model="dialogVisible" title="修改头像" width="800px">
    <UploadAvatar ref="uploadAvatarRef" :url="userInfo?.avatarUrl || defaultAvatar" />

    <template #footer>
      <ElButton type="primary" :loading="avatarLoading" @click="saveAvatar"> 保存 </ElButton>
      <ElButton @click="dialogVisible = false">关闭</ElButton>
    </template>
  </Dialog>
</template>

<style lang="less" scoped>
.avatar {
  position: relative;

  &::after {
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    width: 100%;
    height: 100%;
    font-size: 50px;
    color: #fff;
    background-color: rgb(0 0 0 / 40%);
    border-radius: 50%;
    content: '+';
    opacity: 0;
    justify-content: center;
    align-items: center;
  }

  &:hover {
    &::after {
      opacity: 1;
    }
  }
}
</style>
