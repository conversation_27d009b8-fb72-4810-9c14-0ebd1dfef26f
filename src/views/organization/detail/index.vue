<template>
  <ContentWrap>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">机构基础信息</div>
      <el-divider border-style="double" style="margin: 0 10px 0 calc(10px + 2em)" />
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item label="机构类型" width="240" :span="2">
        <el-checkbox v-model="orgTypeMap[6]" label="医疗机构" />
        <el-checkbox v-model="orgTypeMap[1]" label="职业卫生技术服务机构" />
        <el-checkbox v-model="orgTypeMap[2]" label="职业病健康检查机构" />
        <el-checkbox v-model="orgTypeMap[3]" label="职业病诊断机构" />
        <el-checkbox v-model="orgTypeMap[4]" label="职业病鉴定机构" />
        <el-checkbox v-model="orgTypeMap[5]" label="放射性卫生服务机构" />
        <el-checkbox v-model="orgTypeMap[7]" label="疾控中心" />
      </el-descriptions-item>
      <el-descriptions-item label="机构名称" width="240">
        <el-input v-model="formData.cname" />
      </el-descriptions-item>
      <el-descriptions-item label="地区" width="240">
        <el-input v-model="formData.area" />
      </el-descriptions-item>
      <el-descriptions-item label="统一社会信用代码" width="240">
        <el-input v-model="formData.code" />
      </el-descriptions-item>
      <el-descriptions-item label="单位类型" width="240">
        <el-input v-model="formData.industry" />
      </el-descriptions-item>
      <el-descriptions-item label="法人" width="240">
        <el-input v-model="formData.legalPerson" />
      </el-descriptions-item>
      <el-descriptions-item label="法人代表身份证号码" width="240">
        <el-input v-model="formData.legalPersonId" />
      </el-descriptions-item>
      <el-descriptions-item label="职务和职称" width="240">
        <el-input v-model="formData.legalPersonTitle" />
      </el-descriptions-item>
      <el-descriptions-item label="邮政编码" width="240">
        <el-input v-model="formData.zipCode" />
      </el-descriptions-item>
      <el-descriptions-item label="传真号码" width="240">
        <el-input v-model="formData.fax" />
      </el-descriptions-item>
      <el-descriptions-item label="电子邮箱" width="240">
        <el-input v-model="formData.email" />
      </el-descriptions-item>
      <el-descriptions-item label="是否启用" width="240">
        <el-switch v-model="formData.state" />
      </el-descriptions-item>
      <el-descriptions-item width="240" />
      <el-descriptions-item label="备注" width="240" :span="2">
        <el-input v-model="formData.remark" />
      </el-descriptions-item>
    </el-descriptions>

    <div>
      <div style="width: 100%; height: 10px"></div>
      <div class="titleFlag">
        <div class="tf_icon"></div>
        <div class="tf_text">机构详细信息</div>
        <el-divider border-style="double" style="margin: 0 10px 0 calc(10px + 2em)" />
      </div>
    </div>

    <el-collapse v-model="activeNames">
      <el-collapse-item title="医疗机构" name="5" v-show="orgTypeMap[6]">
        <el-descriptions border :column="2">
          <el-descriptions-item label="医疗机构许可证编号" width="240" :span="2">
            <el-input v-model="medicalInstitution.medicalInstitutionLicenseNumber" />
          </el-descriptions-item>
          <el-descriptions-item label="单位类型" width="240">
            <el-input v-model="medicalInstitution.unitType" />
          </el-descriptions-item>
          <el-descriptions-item label="管理类型" width="240">
            <el-input v-model="medicalInstitution.managementType" />
            <!-- 营利性医疗机构
非营利性医疗机构 -->
          </el-descriptions-item>
          <el-descriptions-item label="单位级别" width="240">
            <el-input v-model="medicalInstitution.unitLevel" />
          </el-descriptions-item>
          <el-descriptions-item label="单位分级" width="240">
            <el-select v-model="medicalInstitution.unitGrade" placeholder="Select">
              <el-option :label="'一级'" :value="'一级'" />
              <el-option :label="'二级'" :value="'二级'" />
              <el-option :label="'三级'" :value="'三级'" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="单位分等" width="240">
            <el-select v-model="medicalInstitution.unitGrade2" placeholder="Select">
              <el-option :label="'甲等'" :value="'甲等'" />
              <el-option :label="'乙等'" :value="'乙等'" />
              <el-option :label="'丙等'" :value="'丙等'" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="经营类型" width="240">
            <el-select v-model="medicalInstitution.businessType" placeholder="Select">
              <el-option :label="'公立'" :value="'公立'" />
              <el-option :label="'民营'" :value="'民营'" />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
      </el-collapse-item>

      <el-collapse-item title="职业卫生技术服务机构" name="1" v-show="orgTypeMap[1]">
        <el-descriptions border :column="2">
          <el-descriptions-item label="资质" width="240">
            <el-select v-model="diseaseHealthCheckInstitution.grade" placeholder="Select">
              <el-option :label="'甲级'" :value="'甲级'" />
              <el-option :label="'乙级'" :value="'乙级'" />
              <el-option :label="'丙级'" :value="'丙级'" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="资质证书/备案编号" width="240">
            <el-input v-model="diseaseHealthCheckInstitution.recordNo" />
          </el-descriptions-item>
          <el-descriptions-item label="证书有效期至" width="240" :span="2">
            <el-date-picker
              v-model="diseaseHealthCheckInstitution.validityDate"
              type="date"
              placeholder="选择日期"
            />
          </el-descriptions-item>
          <el-descriptions-item label="可以开展的职业健康检查类别及项目" width="240" :span="2">
            <el-select v-model="diseaseHealthCheckInstitution.itemList" placeholder="Select">
              <el-option :label="'测试'" :value="'测试'" />
              <!-- 粉尘
化学因素
物理因素
放射性因素
生物因素
其他因素 -->
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="业务范围" width="240" :span="2">
            <el-select v-model="diseaseHealthCheckInstitution.businessScope" placeholder="Select">
              <el-option
                v-for="item in businessScopeOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="经技术评审审核认定的检测项目" width="240" :span="2">
            <el-button type="primary"> 查看/编辑 </el-button>
          </el-descriptions-item>
          <el-descriptions-item
            label="达到技术评审考核评估要求的专业技术人员名单"
            width="240"
            :span="2"
          >
            <el-button type="primary"> 查看/编辑 </el-button>
          </el-descriptions-item>
        </el-descriptions>
      </el-collapse-item>

      <el-collapse-item title="职业病健康检查机构" name="2" v-show="orgTypeMap[2]">
        <el-descriptions border :column="2">
          <el-descriptions-item label="单位级别" width="240">
            <el-select
              v-model="occupationalHealthTechnicalServiceInstitution.level"
              placeholder="Select"
            >
              <el-option :label="'国家级'" :value="'国家级'" />
              <el-option :label="'省级'" :value="'省级'" />
              <el-option :label="'市级'" :value="'市级'" />
              <el-option :label="'县级'" :value="'县级'" />
              <el-option :label="'乡镇级'" :value="'乡镇级'" />
              <el-option :label="'村级(行政)'" :value="'村级(行政)'" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="资质证书/备案编号" width="240">
            <el-input v-model="occupationalHealthTechnicalServiceInstitution.recordNo" />
          </el-descriptions-item>
          <el-descriptions-item label="可以开展的职业健康检查类别及项目" width="240" :span="2">
            <el-select
              v-model="occupationalHealthTechnicalServiceInstitution.itemList"
              placeholder="Select"
            >
              <el-option :label="'TEST'" :value="'TEST'" />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
      </el-collapse-item>

      <el-collapse-item title="职业病诊断机构" name="3" v-show="orgTypeMap[3]">
        <el-descriptions border :column="2">
          <el-descriptions-item label="资质证书/备案编号" width="240" :span="2">
            <el-input v-model="occupationalDiseaseDiagnosisInstitution.recordNo" />
          </el-descriptions-item>
          <el-descriptions-item label="可以开展的职业健康检查类别及项目" width="240" :span="2">
            <el-select
              v-model="occupationalDiseaseDiagnosisInstitution.itemList"
              placeholder="Select"
            >
              <el-option :label="'TEST'" :value="'TEST'" />
            </el-select>
          </el-descriptions-item>
        </el-descriptions>
      </el-collapse-item>

      <el-collapse-item title="放射性卫生服务机构" name="4" v-show="orgTypeMap[5]">
        <el-descriptions border :column="2">
          <el-descriptions-item label="资质" width="240">
            <el-select v-model="radioHealthServiceInstitution.grade" placeholder="Select">
              <el-option :label="'甲级'" :value="'甲级'" />
              <el-option :label="'乙级'" :value="'乙级'" />
              <el-option :label="'丙级'" :value="'丙级'" />
            </el-select>
          </el-descriptions-item>
          <el-descriptions-item label="资质证书/备案编号" width="240">
            <el-input v-model="radioHealthServiceInstitution.recordNo" />
          </el-descriptions-item>
        </el-descriptions>
      </el-collapse-item>
    </el-collapse>

    <div>
      <div style="width: 100%; height: 10px"></div>
      <div class="titleFlag">
        <div class="tf_icon"></div>
        <div class="tf_text">联系人</div>
        <el-divider border-style="double" style="margin: 0 10px" />
        <el-button type="primary" @click="addContact">添加联系人</el-button>
      </div>
    </div>

    <el-collapse v-model="activeContracts">
      <el-collapse-item
        v-for="(item, i) in contacts"
        :key="i"
        :title="item.name || '新联系人'"
        :name="i.toString()"
      >
        <el-descriptions border :column="2">
          <el-descriptions-item label="联系人姓名" width="240">
            <el-input v-model="item.name" placeholder="请输入联系人姓名" />
          </el-descriptions-item>
          <el-descriptions-item label="手机号码" width="240">
            <el-input v-model="item.phone" placeholder="请输入手机号码" />
          </el-descriptions-item>
          <el-descriptions-item label="固定电话" width="240">
            <el-input v-model="item.fixedPhone" placeholder="请输入固定电话" />
          </el-descriptions-item>
          <el-descriptions-item label="电子邮箱" width="240">
            <el-input v-model="item.email" placeholder="请输入电子邮箱" />
          </el-descriptions-item>
        </el-descriptions>
        <div class="text-right mt-2">
          <el-button type="danger" @click="removeContact(i)">删除</el-button>
        </div>
      </el-collapse-item>
    </el-collapse>

    <div style="display: flex; justify-content: center; margin-top: 1em">
      <el-button type="primary" @click="handleSubmit">保存</el-button>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import {
  ElSelect,
  ElOption,
  ElButton,
  ElCollapse,
  ElCollapseItem,
  ElCheckbox,
  ElDivider,
  ElInput,
  ElDescriptions,
  ElDescriptionsItem,
  ElDatePicker,
  ElSwitch,
  ElMessage
} from 'element-plus'
import { ref, onMounted, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import {
  getServiceOrgDetail,
  createServiceOrg,
  updateServiceOrg
} from '@/api/organization/serviceOrg'
import { ElLoading } from 'element-plus'

const route = useRoute()
const router = useRouter()

// 表单数据
const formData = ref({
  cname: '',
  area: '',
  code: '',
  industry: '',
  legalPerson: '',
  legalPersonId: '',
  legalPersonTitle: '',
  zipCode: '',
  fax: '',
  email: '',
  state: true,
  remark: ''
})

const activeNames = ref<string[]>(['5'])
const activeContracts = ref<string[]>(['0', '1'])

// 机构类型数组，存储选中的类型ID
const orgTypes = ref<number[]>([])

// 机构类型映射，用于UI绑定
const orgTypeMap = ref({
  1: false, // '职业卫生技术服务机构'
  2: false, // '职业病健康检查机构'
  3: false, // '职业病诊断机构'
  4: false, // '职业病鉴定机构'
  5: false, // '放射性卫生服务机构'
  6: false, // '医疗机构'
  7: false // '疾控中心'
})

// 监听orgTypeMap变化，更新orgTypes数组
watch(
  orgTypeMap,
  (newVal) => {
    orgTypes.value = Object.entries(newVal)
      .filter(([_, isSelected]) => isSelected)
      .map(([key]) => Number(key))
  },
  { deep: true }
)

const businessScopeOptions = [
  {
    value: '采矿业',
    label: '采矿业'
  },
  {
    value: '化工、石化及医药',
    label: '化工、石化及医药'
  },
  {
    value: '冶金、建材',
    label: '冶金、建材'
  },
  {
    value: '机械制造、电力、纺织、建筑和交通运输等行业领域',
    label: '机械制造、电力、纺织、建筑和交通运输等行业领域'
  },
  {
    value: '核设施',
    label: '核设施'
  },
  {
    value: '核技术工业应用',
    label: '核技术工业应用'
  }
]

// 职业病健康检查机构
const occupationalHealthTechnicalServiceInstitution = ref({
  level: '', //单位级别
  recordNo: '', //资质证书/备案编号
  itemList: [] //可以开展的职业健康检查类别及项目 Tree
})

// 职业卫生技术服务机构
const diseaseHealthCheckInstitution = ref({
  grade: '', //资质 甲级|乙级|丙级
  recordNo: '', //资质证书/备案编号
  validityDate: '', //证书有效期至 Date
  itemList: [], //可以开展的职业健康检查类别及项目 Tree
  businessScope: '', //业务范围 select
  items: [], //经技术评审审核认定的检测项目 form
  persons: [] //达到技术评审考核评估要求的专业技术人员名单 table
})

// 职业病诊断机构
const occupationalDiseaseDiagnosisInstitution = ref({
  recordNo: '', //资质证书/备案编号
  itemList: [] //可以开展的职业健康检查类别及项目 Tree
})

// 放射性卫生服务机构
const radioHealthServiceInstitution = ref({
  grade: '', //资质
  recordNo: '' //资质证书/备案编号
})

//  医疗机构
const medicalInstitution = ref({
  medicalInstitutionLicenseNumber: '',
  unitType: '',
  managementType: '',
  unitLevel: '',
  unitGrade: '',
  unitGrade2: '',
  businessType: ''
})

// 联系人列表
const contacts = ref<{ name: string; phone: string; fixedPhone: string; email: string }[]>([])
// 添加联系人
const addContact = () => {
  contacts.value.push({
    name: '',
    phone: '',
    fixedPhone: '',
    email: ''
  })
  // 打开新添加的联系人折叠面板
  activeContracts.value.push((contacts.value.length - 1).toString())
}

// 删除联系人
const removeContact = (index: number) => {
  contacts.value.splice(index, 1)
}

// 加载状态
const loading = ref(false)
// 当前编辑的ID
const currentId = ref('')

// 获取数据
const getData = async (id: string) => {
  if (!id) return

  currentId.value = id
  loading.value = true

  try {
    const loadingInstance = ElLoading.service({
      target: '.el-descriptions',
      text: '加载中...',
      background: 'rgba(255, 255, 255, 0.7)'
    })

    const { data } = await getServiceOrgDetail(id)

    // 填充表单数据
    formData.value = {
      cname: data.cname || '',
      area: data.area || '',
      code: data.code || '',
      industry: data.industry || '',
      legalPerson: data.legalPerson || '',
      legalPersonId: data.legalPersonId || '',
      legalPersonTitle: data.legalPersonTitle || '',
      zipCode: data.zipCode || '',
      fax: data.fax || '',
      email: data.email || '',
      state: data.state === true || data.state === 1,
      remark: data.remark || ''
    }

    // 填充机构类型
    orgTypes.value = data.orgTypes || []
    // 根据数组更新映射
    Object.keys(orgTypeMap.value).forEach((key) => {
      orgTypeMap.value[key] = orgTypes.value.includes(Number(key))
    })

    // 填充各类机构详细信息
    if (data.medicalInstitution) {
      medicalInstitution.value = {
        ...medicalInstitution.value,
        ...data.medicalInstitution
      }
    }

    if (data.occupationalHealthTechnicalServiceInstitution) {
      occupationalHealthTechnicalServiceInstitution.value = {
        ...occupationalHealthTechnicalServiceInstitution.value,
        ...data.occupationalHealthTechnicalServiceInstitution
      }
    }

    if (data.diseaseHealthCheckInstitution) {
      diseaseHealthCheckInstitution.value = {
        ...diseaseHealthCheckInstitution.value,
        ...data.diseaseHealthCheckInstitution
      }
    }

    if (data.occupationalDiseaseDiagnosisInstitution) {
      occupationalDiseaseDiagnosisInstitution.value = {
        ...occupationalDiseaseDiagnosisInstitution.value,
        ...data.occupationalDiseaseDiagnosisInstitution
      }
    }

    if (data.radioHealthServiceInstitution) {
      radioHealthServiceInstitution.value = {
        ...radioHealthServiceInstitution.value,
        ...data.radioHealthServiceInstitution
      }
    }

    // 填充联系人信息
    if (data.contacts && Array.isArray(data.contacts)) {
      contacts.value = data.contacts
    }

    // 根据机构类型显示对应的折叠面板
    activeNames.value = []
    if (orgTypeMap.value[1]) activeNames.value.push('1')
    if (orgTypeMap.value[2]) activeNames.value.push('2')
    if (orgTypeMap.value[3]) activeNames.value.push('3')
    if (orgTypeMap.value[4]) activeNames.value.push('4')
    if (orgTypeMap.value[5]) activeNames.value.push('5')
    if (orgTypeMap.value[6]) activeNames.value.push('6')

    loadingInstance.close()
  } catch (error) {
    console.error('获取机构详情失败:', error)
    ElMessage.error('获取机构详情失败')
  } finally {
    loading.value = false
  }
}

// 保存
const handleSubmit = async () => {
  // 表单验证
  if (!formData.value.cname) {
    ElMessage.warning('请输入机构名称')
    return
  }

  if (!formData.value.code) {
    ElMessage.warning('请输入社会信用代码')
    return
  }

  // 准备提交的数据
  const submitData = {
    // 基础信息
    ...formData.value,
    state: formData.value.state,

    // 机构类型
    orgTypes: orgTypes.value,

    // 各类机构详细信息
    medicalInstitution: orgTypeMap.value[6] ? medicalInstitution.value : undefined,
    occupationalHealthTechnicalServiceInstitution: orgTypeMap.value[1]
      ? occupationalHealthTechnicalServiceInstitution.value
      : undefined,
    diseaseHealthCheckInstitution: orgTypeMap.value[2]
      ? diseaseHealthCheckInstitution.value
      : undefined,
    occupationalDiseaseDiagnosisInstitution: orgTypeMap.value[3]
      ? occupationalDiseaseDiagnosisInstitution.value
      : undefined,
    radioHealthServiceInstitution: orgTypeMap.value[5]
      ? radioHealthServiceInstitution.value
      : undefined,

    // 联系人
    contacts: contacts.value
  }

  try {
    loading.value = true
    const loadingInstance = ElLoading.service({
      target: '.el-descriptions',
      text: '保存中...',
      background: 'rgba(255, 255, 255, 0.7)'
    })

    // 根据路由参数判断是新增还是编辑
    const type = route.query.type as string

    if (type === 'add') {
      // 新增
      const res = await createServiceOrg(submitData)
      if (res.code === 0) {
        ElMessage.success('更新成功')
      } else {
        throw new Error(res.msg)
      }
    } else {
      // 编辑
      if (!currentId.value) {
        ElMessage.error('缺少机构ID，无法更新')
        return
      }
      const res = await updateServiceOrg(currentId.value, submitData)
      if (res.code === 0) {
        ElMessage.success('更新成功')
      } else {
        throw new Error(res.msg)
      }
    }

    loadingInstance.close()
    // 返回列表页
    router.push('/organization/list')
  } catch (error: any) {
    console.error('保存失败:', error)
    ElMessage.error(error.message || '保存失败')
  } finally {
    loading.value = false
  }
}

// 页面加载时获取数据
onMounted(() => {
  const id = route.query.id as string
  if (id) {
    getData(id)
  }
})
</script>

<style lang="less" scoped>
@import url('@/styles/institutionTitle.less');

.titleFlag {
  margin-top: 1em;
}
</style>
