<template>
  <ContentWrap>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">查询条件</div>
      <el-divider border-style="double" />
    </div>
    <el-form :inline="true" :model="searchData" class="demo-form-inline" label-width="133px">
      <el-form-item label="机构名称">
        <el-input v-model="searchData.institutionName" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="统一社会信用代码">
        <el-input v-model="searchData.code" placeholder="请输入" clearable />
      </el-form-item>

      <el-form-item>
        <el-button type="primary" plain @click="search">查询</el-button>
        <el-button type="warning" plain @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">机构列表</div>
      <el-divider border-style="double" style="margin: 0 10px" />
      <el-button type="primary" @click="goDetail('add')">新增机构</el-button>
      <el-button type="danger" plain @click="batchDelete()" :disabled="selectedRows.length === 0"
        >批量删除</el-button
      >
    </div>
    <!-- 表格部分 -->
    <el-table
      style="width: 100%"
      :data="institutionRecordList"
      row-key="id"
      border
      @selection-change="handleSelectionChange"
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" label="序号" width="60" align="center">
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="institutionName"
        tooltip-effect
        label="机构名称"
        min-width="100"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="recordType"
        label="统一社会信用代码"
        :show-overflow-tooltip="true"
        min-width="110"
      />
      <el-table-column prop="address" label="法人" :show-overflow-tooltip="true" min-width="110" />
      <el-table-column
        prop="institutionNature"
        label="机构类型"
        :show-overflow-tooltip="true"
        min-width="110"
      />

      <el-table-column label="操作" min-width="110" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="goDetail('edit', scope.row.id)"
            >编辑</el-button
          >
          <el-button type="success" size="small" @click="goDetail('view', scope.row.id)"
            >详情</el-button
          >
          <el-button type="danger" plain size="small" @click="deleteMenu(scope.row)"
            >删除</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top: 20px"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      layout="sizes, prev, pager, next, jumper, total"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElMessage,
  ElMessageBox,
  ElPagination,
  ElDivider,
  ElForm,
  ElFormItem,
  ElInput
} from 'element-plus'
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { getServiceOrgList, deleteServiceOrg } from '@/api/organization/serviceOrg'
import { ElLoading } from 'element-plus'
const router = useRouter()

const searchData = ref({
  institutionName: '',
  code: ''
})

// 加载状态
const loading = ref(false)

const institutionRecordList = ref<any>([]) // 表格数据
const selectedRows = ref<any>([]) // 选中的行

// 获取数据
const getData = async (params?: any) => {
  try {
    loading.value = true
    const loadingInstance = ElLoading.service({
      target: '.el-table',
      text: '加载中...',
      background: 'rgba(255, 255, 255, 0.7)'
    })

    // 准备查询参数
    const queryParams = {
      page: params?.pageNum || currentPage.value,
      pageSize: params?.pageSize || pageSize.value,
      cname: params?.institutionName || searchData.value.institutionName,
      code: params?.code || searchData.value.code
    }

    // 调用API获取数据
    const { data } = await getServiceOrgList(queryParams)

    // 处理返回的数据
    institutionRecordList.value = data.list.map((item: any) => ({
      id: item._id,
      institutionName: item.cname,
      recordType: item.code,
      address: item.legalPerson,
      institutionNature: getInstitutionTypes(item)
    }))

    total.value = data.total
    loadingInstance.close()
  } catch (error) {
    console.error('获取机构列表失败:', error)
    ElMessage.error('获取机构列表失败')
  } finally {
    loading.value = false
  }
}

// 获取机构类型字符串
const getInstitutionTypes = (item: any): string => {
  const types: string[] = []
  const typeMap = {
    1: '职业卫生技术服务机构',
    2: '职业病健康检查机构',
    3: '职业病诊断机构',
    4: '职业病鉴定机构',
    5: '放射性卫生服务机构',
    6: '医疗机构',
    7: '疾控中心'
  }

  // 如果有orgTypes数组，使用新格式
  if (item.orgTypes && Array.isArray(item.orgTypes)) {
    item.orgTypes.forEach((typeId: number) => {
      if (typeMap[typeId]) {
        types.push(typeMap[typeId])
      }
    })
  } else {
    // 兼容旧格式
    if (item.type1) types.push(typeMap[1])
    if (item.type2) types.push(typeMap[2])
    if (item.type3) types.push(typeMap[3])
    if (item.type4) types.push(typeMap[4])
    if (item.type5) types.push(typeMap[5])
    if (item.type6) types.push(typeMap[6])
    if (item.type7) types.push(typeMap[7])
  }

  return types.join('、')
}

// 搜索
const search = () => {
  currentPage.value = 1
  pageSize.value = 10
  getData({
    ...searchData.value,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}

// 重置查询条件
const reset = () => {
  searchData.value = {
    institutionName: '',
    code: ''
  }
  currentPage.value = 1
  pageSize.value = 10
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}

// 删除单个机构
const deleteMenu = async (row: any) => {
  if (!row) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除机构："${row.institutionName}"?`, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })

    loading.value = true
    await deleteServiceOrg([row.id])
    ElMessage.success('删除成功')

    // 刷新数据
    getData({
      pageNum: currentPage.value,
      pageSize: pageSize.value
    })
  } catch (error) {
    if (error === 'cancel') {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      })
    } else {
      console.error('删除失败:', error)
      ElMessage.error('删除失败')
    }
  } finally {
    loading.value = false
  }
}

// 表格选择
const handleSelectionChange = (selection: any[]) => {
  selectedRows.value = selection
}

// 批量删除
const batchDelete = async () => {
  if (selectedRows.value.length === 0) {
    ElMessage.warning('请选择要删除的记录')
    return
  }

  try {
    await ElMessageBox.confirm(`确认删除选中的 ${selectedRows.value.length} 条记录吗？`, '提示', {
      confirmButtonText: '确认',
      cancelButtonText: '取消',
      type: 'warning'
    })

    loading.value = true
    const ids = selectedRows.value.map((row: any) => row.id)
    await deleteServiceOrg(ids)
    ElMessage.success('批量删除成功')

    // 刷新数据
    getData({
      pageNum: currentPage.value,
      pageSize: pageSize.value
    })
  } catch (error) {
    if (error === 'cancel') {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      })
    } else {
      console.error('批量删除失败:', error)
      ElMessage.error('批量删除失败')
    }
  } finally {
    loading.value = false
  }
}

// #region 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleSizeChange = (val: number) => {
  pageSize.value = val
  currentPage.value = 1
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
const handleCurrentChange = (val: number) => {
  currentPage.value = val
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
// #endregion

const goDetail = (type: 'add' | 'edit' | 'view', id?: string) => {
  router.push({
    path: '/organization/detail',
    query: {
      type,
      id
    }
  })
}

// 页面加载时获取数据
onMounted(() => {
  getData({ pageNum: currentPage.value, pageSize: pageSize.value })
})
</script>

<style lang="less">
@import url('@/styles/institutionTitle.less');
</style>
