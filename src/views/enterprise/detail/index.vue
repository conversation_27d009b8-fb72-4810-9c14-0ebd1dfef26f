<template>
  <ContentWrap>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">基本信息</div>
      <el-divider border-style="double" />
    </div>
    <el-descriptions border :column="2" :label-width="240">
      <el-descriptions-item label="单位名称" min-width="80">{{
        formData.cname
      }}</el-descriptions-item>
      <el-descriptions-item label="统一社会信用代码" min-width="80">{{
        formData.code
      }}</el-descriptions-item>
      <el-descriptions-item label="法人" min-width="80">{{ formData.corp }}</el-descriptions-item>
      <el-descriptions-item label="联系人" min-width="80">{{
        formData.contract
      }}</el-descriptions-item>
      <el-descriptions-item label="联系方式" min-width="80">{{
        formData.phoneNum
      }}</el-descriptions-item>
      <el-descriptions-item label="地区" min-width="80">{{
        formData.districtRegAdd.join('/')
      }}</el-descriptions-item>
      <el-descriptions-item label="地址" min-width="80">{{ formData.regAdd }}</el-descriptions-item>
      <el-descriptions-item label="注册类型" min-width="80">{{
        formData.regType
      }}</el-descriptions-item>
      <el-descriptions-item label="行业类别" min-width="80">
        {{
          formData.industryCategory && formData.industryCategory.length >= 1
            ? getIndustryCategoryName(formData.industryCategory)
            : ''
        }}
      </el-descriptions-item>
      <el-descriptions-item label="规模" min-width="80">{{
        formData.companyScale
      }}</el-descriptions-item>
    </el-descriptions>

    <!-- 生产中使用和产生的化学物质信息 -->

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">接害情况</div>
      <el-divider border-style="double" style="margin: 0 10px" />
    </div>
    <el-table
      style="width: 100%"
      :data="formData?.statistics?.stationStatistics ?? []"
      border
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column
        prop="workshopName"
        label="车间"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column
        prop="workspaceName"
        label="车间"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column
        prop="workTypeName"
        label="工种"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column
        prop="harmFactors"
        label="危害因素"
        :show-overflow-tooltip="true"
        min-width="160"
      />
      <el-table-column
        prop="employeeCount"
        label="接害人数"
        :show-overflow-tooltip="true"
        min-width="40"
      />
    </el-table>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">危害因素检测</div>
      <el-divider border-style="double" style="margin: 0 10px 0 calc(10px + 2em)" />
    </div>
    <!-- 表格部分 -->
    <el-table
      style="width: 100%"
      :data="jobHealthData"
      row-key="id"
      border
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column
        prop="projectName"
        label="项目名称"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column
        prop="projectNumber"
        label="编号"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column prop="year" label="年度" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column prop="date" label="检测时间" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column
        prop="serviceType"
        label="资质证书"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          <el-tag v-if="row.serviceType" type="success">已上传</el-tag>
          <el-tag v-else type="danger">未上传</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="report" label="报告书" :show-overflow-tooltip="true" min-width="80">
        <template #default="{ row }">
          <el-tag v-if="row.report" type="success">已上传</el-tag>
          <el-tag v-else type="danger">未上传</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="checkResult"
        label="检测结果"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          <el-tag v-if="row.checkResult" type="success">已上传</el-tag>
          <el-tag v-else type="danger">未上传</el-tag>
        </template>
      </el-table-column>
    </el-table>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">劳动者健康监护</div>
      <el-divider border-style="double" style="margin: 0 10px 0 calc(10px + 3em)" />
    </div>
    <el-table
      style="width: 100%"
      :data="healthcheckData"
      row-key="_id"
      border
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="checkNo" label="编号" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column prop="name" label="姓名" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column
        prop="checkType"
        label="体检类型"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          {{ row.checkType === '1' ? '职业健康体检' : '一般体检' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="examType"
        label="检查类型"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          {{ getExamTypeText(row.examType) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="registerTime"
        label="体检日期"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          {{ formatDate(row.registerTime) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="checkHazardFactors"
        label="危害因素"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          {{ getArrName(row.checkHazardFactors) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="jobConclusion"
        label="结论"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          {{ getJobconclusion(row.jobConclusion) }}
        </template>
      </el-table-column>
      <el-table-column
        prop="isRecheck"
        label="初检/复查"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          {{ row.isRecheck ? '复查' : '初检' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="120" fixed="right">
        <template #default="{ row }">
          <el-button type="primary" link @click="showExamDetail(row)">查看详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <!-- 体检详情弹窗 -->
    <el-dialog v-model="examDetailVisible" title="体检详情" width="70%">
      <div class="exam-detail">
        <div class="detail-section">
          <h3>检测结果</h3>
          <div
            v-for="dept in currentExamDetail?.checkDepartments"
            :key="dept._id"
            class="department"
          >
            <h4>{{ dept.departmentName }}</h4>
            <el-table :data="dept.checkProjects" border>
              <el-table-column prop="projectName" label="检查项目" width="180">
                <template #default="{ row }">
                  <div class="project-name">
                    {{ row.projectName }}
                    <el-tag size="small" type="info" v-if="getProjectDetail(row.projectId)">
                      {{ getProjectDetail(row.projectId)?.projectNumber }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="检查结果" min-width="300">
                <template #default="{ row }">
                  <div class="check-results">
                    <div v-for="item in row.checkItems" :key="item._id" class="check-item">
                      <div class="item-header">
                        <span class="item-name">{{
                          getItemDetail(item.itemId)?.projectName || '未知项目'
                        }}</span>
                        <el-tag
                          size="small"
                          :type="item.conclusion === '正常' ? 'success' : 'warning'"
                        >
                          {{ item.conclusion || '-' }}
                        </el-tag>
                      </div>
                      <div class="item-content">
                        <div class="result-value">
                          检测值：{{ item.result }}
                          <span v-if="getItemDetail(item.itemId)">
                            {{ getItemDetail(item.itemId)?.msrunt }}
                          </span>
                        </div>
                        <div class="standard-range" v-if="getItemDetail(item.itemId)">
                          参考范围：{{ getItemDetail(item.itemId)?.standardValueMin }} -
                          {{ getItemDetail(item.itemId)?.standardValueMax }}
                          {{ getItemDetail(item.itemId)?.msrunt }}
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="dept-summary">科室总结：{{ dept.summary }}</div>
          </div>
        </div>
        <div class="detail-section">
          <h3>体检总结</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="健康总结">{{
              currentExamDetail?.healthSummary
            }}</el-descriptions-item>
            <el-descriptions-item label="意见建议">{{
              currentExamDetail?.suggestion
            }}</el-descriptions-item>
            <el-descriptions-item label="职检总结">{{
              currentExamDetail?.jobSummary
            }}</el-descriptions-item>
            <el-descriptions-item label="职检结论">
              <el-tag
                v-for="conclusion in currentExamDetail?.jobConclusion"
                :key="conclusion"
                class="conclusion-tag"
              >
                {{ getConclusionText(conclusion) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">劳动者职业病发生情况</div>
      <el-divider border-style="double" style="margin: 0 10px 0 calc(10px + 6em)" />
    </div>
    <el-table
      style="width: 100%"
      :data="odiseaseData"
      row-key="id"
      border
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="name" label="姓名" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column prop="sex" label="性别" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column prop="birthday" label="年龄" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column prop="IDNum" label="身份证" :show-overflow-tooltip="true" min-width="80" />
      <!-- <el-table-column prop="phoneNum" label="手机号" :show-overflow-tooltip="true" min-width="80" /> -->
      <el-table-column prop="workspace" label="车间" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column
        prop="diseaseName"
        label="职业病名"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column prop="age" label="接害工龄" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column
        prop="hospital"
        label="诊断机构"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <!-- <el-table-column prop="decideDate" label="诊断日期" :show-overflow-tooltip="true" min-width="80" /> -->
      <el-table-column
        prop="status"
        label="处理情况"
        :show-overflow-tooltip="true"
        min-width="80"
      />
    </el-table>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">员工档案</div>
      <el-divider border-style="double" style="margin: 0 10px" />
    </div>
    <el-table
      style="width: 100%"
      :data="employees"
      row-key="id"
      border
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="index" label="序号" width="60" align="center" />
      <el-table-column prop="name" label="姓名" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column prop="gender" label="性别" :show-overflow-tooltip="true" min-width="80">
        <template #default="{ row }">
          {{ row.gender === '0' ? '男' : row.gender === '1' ? '女' : '' }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="departs" label="部门" :show-overflow-tooltip="true" min-width="80">
        <template #default="{ row }">
          {{ row.departs }}
        </template>
      </el-table-column> -->
      <el-table-column prop="workType" label="工种" :show-overflow-tooltip="true" min-width="80">
        <template #default="{ row }">
          {{ row.workType }}
        </template>
      </el-table-column>
      <el-table-column
        prop="workStart"
        label="入职时间"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          {{ getFormatDate(row.workStart) }}
        </template>
      </el-table-column>
      <el-table-column prop="workYears" label="工龄" :show-overflow-tooltip="true" min-width="80">
        <template #default="{ row }">
          {{ row.workYears }}
        </template>
      </el-table-column>
      <el-table-column prop="IDNum" label="身份证号" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column
        prop="phoneNum"
        label="手机号"
        :show-overflow-tooltip="true"
        min-width="80"
      />
    </el-table>
    <div style="display: flex; justify-content: center; margin-top: 1em">
      <el-button plain @click="back">返回</el-button>
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import moment from 'moment'
import { ContentWrap } from '@/components/ContentWrap'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElDivider,
  ElDescriptions,
  ElDescriptionsItem,
  ElTag,
  ElDialog
} from 'element-plus'
import { ref } from 'vue'
import {
  getEnterpriseDetail,
  getEmployeeList,
  employeeRegisterlist,
  getItemList
} from '@/api/employee'
import { useRoute } from 'vue-router'
import { getIndustryCategory } from '@/api/common'
const route = useRoute()

const enterpriseId = ref(route.query.id)

const formData = ref<any>({
  _id: '',
  cname: '',
  code: '',
  legalPerson: '',
  contact: '',
  phoneNum: '',
  districtRegAdd: [],
  area: '',
  address: '',
  industry: '',
  state: ''
})

const getData = async (params?: any) => {
  const res = await getEnterpriseDetail(params)
  formData.value = res.data
  const res2 = await getEmployeeList({
    EnterpriseID: formData.value._id,
    pageNum: 1,
    pageSize: 1000
  })
  employees.value = res2.data.list

  // 获取健康监护数据
  await getHealthCheckData()
}
getData(enterpriseId.value)

// 获取体检项目列表
const itemList = ref<any[]>([])

// 获取健康监护数据
const getHealthCheckData = async () => {
  // 获取体检项目列表
  const itemRes = await getItemList()
  itemList.value = itemRes.data || []

  // 获取企业健康监护数据
  const res = await employeeRegisterlist({ EnterpriseID: formData.value._id })
  const list = res.data?.list || []
  if (Array.isArray(list)) {
    healthcheckData.value = list
  } else if (list && typeof list === 'object' && 'data' in list) {
    healthcheckData.value = (list as any).data
  }
}

const industryCategoryOptions = ref<any[]>([])

const getindustryCategoryOptions = async () => {
  const response = await getIndustryCategory()
  industryCategoryOptions.value = response.data
}

const getIndustryCategoryName = (idArr: string[]) => {
  console.log(idArr)
  // 最高四级树结构
  const industryCategory = industryCategoryOptions.value
  let name = ''
  const getName = (arr: any[], idArr: string[]) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      if (item.value === idArr[0]) {
        name += item.label
        if (idArr.length > 1) {
          getName(item.children, idArr.slice(1))
        }
        break
      }
    }
  }
  idArr.length >= 1 && getName(industryCategory, idArr)
  return name
}

// 初始化获取行业类别数据
getindustryCategoryOptions()

const jobHealthData = ref<any>([])

const healthcheckData = ref<any>([])

const odiseaseData = ref<any>([])

const employees = ref<any>([])

import { useRouter } from 'vue-router'
const router = useRouter()
const back = () => {
  router.back()
}

const getFormatDate = (date: string | number | Date) => {
  return date ? moment(date).format('YYYY-MM-DD') : ''
}

// 格式化日期
const formatDate = (dateStr: string) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return date.toLocaleDateString()
}

// 获取检查类型文本
const getExamTypeText = (type: string) => {
  const types = {
    '1': '岗前',
    '2': '在岗',
    '3': '离岗',
    '4': '离岗后',
    '5': '应急'
  }
  return types[type] || type
}

// 获取数组名称
const getArrName = (arr: any[]) => {
  if (!arr || !Array.isArray(arr)) return ''
  return arr.map((item) => item.name).join(',')
}

// 获取职业健康结论
const getJobconclusion = (arr: number[]) => {
  if (!arr || !Array.isArray(arr)) return ''
  let res = ''
  const conclusionMap = {
    '1': '目前未见异常',
    '2': '复查',
    '3': '疑似职业病',
    '4': '禁忌证',
    '5': '其他疾病或异常'
  }
  arr.forEach((item) => {
    res += conclusionMap[item] + ','
  })
  return res.slice(0, -1) // 移除最后一个逗号
}

// 获取结论文本
const getConclusionText = (code: number) => {
  const conclusionMap = {
    '1': '目前未见异常',
    '2': '复查',
    '3': '疑似职业病',
    '4': '禁忌证',
    '5': '其他疾病或异常'
  }
  return conclusionMap[code] || code
}

// 体检详情相关变量
const examDetailVisible = ref(false)
const currentExamDetail = ref<any>(null)

// 查看体检详情
const showExamDetail = (row: any) => {
  currentExamDetail.value = row
  examDetailVisible.value = true
}

// 获取项目详情
const getProjectDetail = (projectId: string) => {
  return itemList.value.find((item) => item._id === projectId) || null
}

// 获取检查项详情
const getItemDetail = (itemId: string) => {
  return itemList.value.find((item) => item._id === itemId) || null
}
</script>

<style lang="less">
@import url('@/styles/institutionTitle.less');

.exam-detail {
  .detail-section {
    margin-bottom: 20px;

    h3 {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }

    h4 {
      margin: 15px 0;
      font-size: 15px;
      color: #606266;
    }
  }

  .department {
    padding: 15px;
    margin-bottom: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .dept-summary {
      margin-top: 10px;
      font-style: italic;
      color: #666;
    }
  }

  .project-name {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .check-results {
    .check-item {
      padding: 8px;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        .item-name {
          font-weight: 500;
          color: #303133;
        }
      }

      .item-content {
        .result-value {
          margin-bottom: 4px;
          font-weight: 500;
          color: #303133;
        }

        .standard-range {
          font-size: 13px;
          color: #909399;
        }
      }
    }
  }

  .conclusion-tag {
    margin-right: 8px;
  }
}
</style>

<!-- <style lang="less" scoped>
:deep(.el-divider) {
  margin-left: 70px !important;
}
</style> -->
