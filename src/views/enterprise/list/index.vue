<template>
  <ContentWrap>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">查询条件</div>
      <el-divider border-style="double" />
    </div>
    <el-row class="searchBox" style="margin-bottom: 20px">
      <div style="display: flex; justify-content: space-between">
        <el-form :inline="true" :model="searchData" class="demo-form-inline" label-width="100px">
          <el-form-item label="时间点">
            <el-date-picker
              v-model="searchData.timePoint"
              type="date"
              placeholder="请选择"
              value-format="YYYY-MM-DD"
              @change="timePointChange"
            />
          </el-form-item>

          <el-form-item label="时间段">
            <el-date-picker
              v-model="searchData.timeRange"
              type="daterange"
              value-format="YYYY-MM-DD"
              format="YYYY 年 MM 月 DD 日"
              range-separator="至"
              start-placeholder="开始时间"
              end-placeholder="结束时间"
              @change="timeRangeChange"
              style="width: calc(100px + 220px * 2)"
            />
          </el-form-item>

          <el-form-item label="单位名称">
            <el-input v-model="searchData.name" placeholder="请输入" clearable />
          </el-form-item>

          <el-form-item label="危害因素分类">
            <!-- <el-input v-model="searchData.harm" placeholder="请输入危害因素名称" clearable /> -->
            <el-select
              v-model="searchData.harm"
              placeholder="请选择危害因素分类"
              style="width: 220px"
              clearable
            >
              <el-option
                v-for="item in harmOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
          </el-form-item>

          <el-form-item label="区域">
            <el-cascader
              v-model="searchData.district"
              :props="districtProps"
              clearable
              :show-all-levels="false"
            />
          </el-form-item>

          <el-form-item label="行业类别">
            <el-cascader
              placeholder="请选择行业类别"
              v-model="searchData.industryCategory"
              :options="industryCategoryOptions"
              :props="{ checkStrictly: true }"
              filterable
              clearable
            />
          </el-form-item>

          <el-form-item>
            <el-button type="primary" plain @click="search()">查询</el-button>
          </el-form-item>

          <el-form-item>
            <el-button type="warning" plain @click="reset()">重置</el-button>
          </el-form-item>
        </el-form>
      </div>
    </el-row>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">企业列表</div>
      <el-divider border-style="double" style="margin: 0 10px" />
      <el-button type="primary" size="small" @click="exportExcel()"> 导 出 </el-button>
      <el-button type="success" size="small" @click="print" :loading="printLoading">
        打 印
      </el-button>
    </div>
    <!-- 表格部分 -->
    <el-table
      style="width: 100%"
      :data="tableData"
      row-key="id"
      border
      id="enterpriseTable"
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="index" label="序号" width="60" align="center">
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="cname"
        tooltip-effect
        label="单位名称"
        min-width="100"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="code"
        label="统一社会信用代码"
        :show-overflow-tooltip="true"
        min-width="100"
      />
      <el-table-column prop="corp" label="法人" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column
        prop="contract"
        label="联系人"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column
        prop="phoneNum"
        label="联系方式"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column
        prop="districtRegAdd"
        label="地区"
        :show-overflow-tooltip="true"
        min-width="80"
      />
      <el-table-column prop="regAdd" label="地址" :show-overflow-tooltip="true" min-width="80" />
      <el-table-column prop="regType" label="注册类型" :show-overflow-tooltip="true" min-width="80">
        <template #default="{ row }">
          {{ row.regType }}
        </template>
      </el-table-column>
      <el-table-column
        prop="industryCategory"
        label="行业类别"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="scope">
          {{
            scope.row.industryCategory &&
            scope.row.industryCategory.length >= 1 &&
            getIndustryCategoryName(scope.row.industryCategory[0])
          }}
        </template>
      </el-table-column>
      <el-table-column
        prop="createTime"
        label="创建时间"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="scope">
          {{ scope.row.createTime ? getFormatDate(scope.row.createTime) : '' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updateTime"
        label="更新时间"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="scope">
          {{ scope.row.updateTime ? getFormatDate(scope.row.updateTime) : '' }}
        </template>
      </el-table-column>
      <el-table-column label="操作" width="80" fixed="right">
        <template #default="scope">
          <el-button type="success" size="small" @click="goDetail(scope.row._id)">详情</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top: 20px"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      layout="sizes, prev, pager, next, jumper, total"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElMessage,
  // ElMessageBox,
  ElSelect,
  ElOption,
  ElInput,
  ElPagination,
  ElDivider,
  ElForm,
  ElFormItem,
  ElDatePicker,
  ElCascader,
  ElRow
} from 'element-plus'
import { ref } from 'vue'
import { getEnterpriseList } from '@/api/employee/index'
import { useRouter } from 'vue-router'
const router = useRouter()
import { getDistrict, getIndustryCategory } from '@/api/common'
import * as XLSX from 'xlsx'
import { saveAs } from 'file-saver'
import printJS from 'print-js'
import moment from 'moment'

// 导出excel
const exportExcel = (data: any[] = [], fileName: string = '用人单位档案') => {
  data = tableData.value.map((item) => {
    return {
      单位名称: item.cname,
      统一社会信用代码: item.code,
      法人: item.corp,
      联系人: item.contract,
      联系方式: item.phoneNum,
      地区: item.districtRegAdd,
      地址: item.regAdd,
      注册类型: item.regType,
      行业类别: item.industryCategory,
      规模: item.companyScale
    }
  })
  if (data.length === 0) {
    ElMessage({
      message: '暂无数据，请重现选择搜索条件',
      type: 'info'
    })
    return
  }
  const worksheet = XLSX.utils.json_to_sheet(data)
  const workbook = XLSX.utils.book_new()
  XLSX.utils.book_append_sheet(workbook, worksheet, 'Sheet1')
  const excelBuffer = XLSX.write(workbook, { bookType: 'xlsx', type: 'array' })
  const blob = new Blob([excelBuffer], { type: 'application/octet-stream' })
  saveAs(blob, `${fileName}.xlsx`)
}

const printLoading = ref(false) // 打印loading
// 打印
const print = () => {
  printLoading.value = true
  // setTimeout(() => {
  //   printLoading.value = false
  //   ElMessage({
  //     message: '示未连接到打印设备',
  //     type: 'info'
  //   })
  // }, 500)
  printJS({
    printable: 'enterpriseTable', // 元素ID
    type: 'html', // 打印类型
    header: '用人单位档案' // 自定义标题
  })
  printLoading.value = false
}

const tableData = ref<any>([]) // 表格

const getData = async (params?: any) => {
  const res = await getEnterpriseList(params)
  tableData.value = res.data?.list || []
  total.value = res.data?.total || 0
}

// #region 查询

// 查询条件
const getEmptySearchData = () => {
  return {
    name: '',
    timePoint: '',
    timeRange: [],
    district: [],
    harm: '',
    industryCategory: ''
  }
}

const harmOptions = ref<any[]>([
  { value: '物理', label: '物理' },
  { value: '放射性因素', label: '放射性因素' },
  { value: '化学', label: '化学' },
  { value: '生物', label: '生物' },
  { value: '粉尘', label: '粉尘' },
  { value: '其他', label: '其他' }
])

const searchData = ref({
  name: '',
  timePoint: '',
  timeRange: [],
  district: [],
  harm: '',
  industryCategory: ''
})

// 查询
const search = () => {
  currentPage.value = 1
  pageSize.value = 10
  getData({
    ...searchData.value,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
// 重置查询条件
const reset = () => {
  searchData.value = getEmptySearchData()
  currentPage.value = 1
  pageSize.value = 10
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}

const getIndustryCategoryName = (idArr: string[]) => {
  // 最高四级树结构
  const industryCategory = industryCategoryOptions.value
  let name = ''
  const getName = (arr: any[], idArr: string[]) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      if (item.value === idArr[0]) {
        name += item.label
        if (idArr.length > 1) {
          getName(item.children, idArr.slice(1))
        }
        break
      }
    }
  }
  idArr.length >= 1 && getName(industryCategory, idArr)
  return name
}

const districtProps = {
  // multiple: true,
  checkStrictly: true,
  lazy: true,
  lazyLoad(node, resolve) {
    const params = {
      level: node.level,
      parent_code: node.data && node.data.area_code
    }
    getDistrict(params).then((response) => {
      try {
        const districts = response.data
        let nodes = districts.map((item) => ({
          value: item.name,
          label: item.name,
          area_code: item.area_code,
          leaf: item.level >= 3,
          disabled: item.name === '市辖区' ? true : false
        }))
        resolve(nodes)
      } catch (e) {
        console.log(e)
      }
    })
  }
}

const getFormatDate = (date) => {
  return moment(date).format('YYYY-MM-DD')
}

const industryCategoryOptions = ref<any[]>([])

const getindustryCategoryOptions = async () => {
  const response = await getIndustryCategory()
  industryCategoryOptions.value = response.data
}

getindustryCategoryOptions()

// #endregion

const timePointChange = () => {
  // 清空时间段查询条件
  searchData.value.timeRange = []
}

const timeRangeChange = () => {
  searchData.value.timePoint = ''
}

// #region 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
// #endregion

getData({ pageNum: currentPage.value, pageSize: pageSize.value })

const goDetail = (id: string) => {
  // 路由跳转
  router.push({ path: '/enterprise/detail', query: { id: id } })
}

// const searchPopoverVisible = ref(false)

// const harmOptions = [
//   {
//     value: '化学',
//     label: '化学',
//     children: [
//       {
//         value: '苯',
//         label: '苯'
//       },
//       {
//         value: '氨',
//         label: '氨'
//       }
//     ]
//   },
//   {
//     value: '物理',
//     label: '物理',
//     children: [
//       {
//         value: '噪声',
//         label: '噪声'
//       },
//       {
//         value: '高温',
//         label: '高温'
//       }
//     ]
//   }
// ]
</script>

<style lang="less" scoped>
@import url('@/styles/institutionTitle.less');

.note {
  color: rgb(151 151 151);
}
</style>
