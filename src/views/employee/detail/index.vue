<template>
  <ContentWrap>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">基本信息</div>
      <el-divider border-style="double" />
    </div>
    <el-descriptions border :column="2">
      <el-descriptions-item label="姓名" width="240">
        <el-input v-model="formData.name" />
      </el-descriptions-item>
      <el-descriptions-item label="证件类型" width="240">
        <el-select v-model="formData.idType" placeholder="Select">
          <el-option :label="'居民身份证'" :value="'1'" />
          <el-option :label="'居民户口簿'" :value="'2'" />
          <el-option :label="'护照'" :value="'3'" />
          <el-option :label="'军官证'" :value="'4'" />
          <el-option :label="'驾驶证'" :value="'5'" />
          <el-option :label="'港澳居民来往内地通行证'" :value="'6'" />
          <el-option :label="'台湾居民来往内地通行证'" :value="'7'" />
          <el-option :label="'其他法定有效证件'" :value="'99'" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="证件号码" width="240">
        <el-input v-model="formData.IDNum" />
      </el-descriptions-item>
      <el-descriptions-item label="性别" width="240">
        <el-select v-model="formData.gender" placeholder="Select">
          <el-option :label="'男'" :value="'0'" />
          <el-option :label="'女'" :value="'1'" />
        </el-select>
      </el-descriptions-item>
      <el-descriptions-item label="出生日期" width="240">
        <el-date-picker v-model="formData.birthDate" type="date" placeholder="选择日期" />
      </el-descriptions-item>
      <el-descriptions-item label="联系电话" width="240">
        <el-input v-model="formData.phoneNum" />
      </el-descriptions-item>
      <el-descriptions-item label="用人单位名称" width="240">
        <el-input v-model="formData.cname" />
      </el-descriptions-item>
      <el-descriptions-item label="用人单位所在区域" width="240">
        <el-input v-model="formData.districtRegAdd" />
      </el-descriptions-item>
    </el-descriptions>

    <!-- 工作经历 -->
    <div v-show="infoSet.workHistory">
      <div style="width: 100%; height: 10px"></div>
      <div class="titleFlag">
        <div class="tf_icon"></div>
        <div class="tf_text">工作经历</div>
        <el-divider border-style="double" style="margin: 0 10px" />
      </div>
      <el-table :data="workExperience" border>
        <el-table-column type="index" label="序号" width="60" align="center" />
        <el-table-column prop="workUnit" label="工作单位" />
        <el-table-column prop="value" label="工作时间" :show-overflow-tooltip="true" min-width="80">
          <template #default="{ row }"> {{ row.entryTime }} - {{ row.leaveTime }} </template>
        </el-table-column>
        <el-table-column
          prop="workshop"
          label="车间"
          :show-overflow-tooltip="true"
          min-width="80"
        />
        <el-table-column
          prop="workType"
          label="工种"
          :show-overflow-tooltip="true"
          min-width="80"
        />
        <el-table-column prop="station" label="岗位" :show-overflow-tooltip="true" min-width="80" />
      </el-table>
    </div>

    <!-- 历次职业病危害检测 -->
    <div v-show="infoSet.diseaseDetection">
      <div style="width: 100%; height: 10px"></div>
      <div class="titleFlag">
        <div class="tf_icon"></div>
        <div class="tf_text">职业病危害检测</div>
        <el-divider border-style="double" style="margin: 0 10px" />
      </div>
      <el-table :data="detectList" border>
        <el-table-column prop="detectDate" label="检测年份" />
        <el-table-column prop="detectType" label="检测类型" />
        <el-table-column prop="detectResult" label="检测结果" />
        <el-table-column prop="detectUnit" label="检测单位" />
      </el-table>
    </div>

    <!-- 历次健康体检信息 -->
    <div v-show="infoSet.examination">
      <div style="width: 100%; height: 10px"></div>
      <div class="titleFlag">
        <div class="tf_icon"></div>
        <div class="tf_text">健康体检信息</div>
        <el-divider border-style="double" style="margin: 0 10px" />
      </div>
      <el-table :data="examList" border>
        <el-table-column prop="checkNo" label="编号" width="180" />
        <el-table-column prop="checkType" label="体检类型" width="120">
          <template #default="{ row }">
            {{ row.checkType === '1' ? '职业健康体检' : '一般体检' }}
          </template>
        </el-table-column>
        <el-table-column prop="examType" label="检查类型" width="120">
          <template #default="{ row }">
            {{ getExamTypeText(row.examType) }}
          </template>
        </el-table-column>
        <el-table-column prop="registerTime" label="体检日期" width="180">
          <template #default="{ row }">
            {{ formatDate(row.registerTime) }}
          </template>
        </el-table-column>
        <el-table-column prop="checkHazardFactors" label="危害因素">
          <template #default="{ row }">
            {{ getArrName(row.checkHazardFactors) }}
          </template>
        </el-table-column>
        <el-table-column prop="jobConclusion" label="结论">
          <template #default="{ row }">
            {{ getJobconclusion(row.jobConclusion) }}
          </template>
        </el-table-column>
        <el-table-column prop="isRecheck" label="初检/复查" width="100">
          <template #default="{ row }">
            {{ row.isRecheck ? '复查' : '初检' }}
          </template>
        </el-table-column>
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="{ row }">
            <el-button type="primary" link @click="showExamDetail(row)">查看详情</el-button>
          </template>
        </el-table-column>
      </el-table>
    </div>

    <!-- 体检详情弹窗 -->
    <el-dialog v-model="examDetailVisible" title="体检详情" width="70%">
      <div class="exam-detail">
        <div class="detail-section">
          <h3>检测结果</h3>
          <div
            v-for="dept in currentExamDetail?.checkDepartments"
            :key="dept._id"
            class="department"
          >
            <h4>{{ dept.departmentName }}</h4>
            <el-table :data="dept.checkProjects" border>
              <el-table-column prop="projectName" label="检查项目" width="180">
                <template #default="{ row }">
                  <div class="project-name">
                    {{ row.projectName }}
                    <el-tag size="small" type="info" v-if="getProjectDetail(row.projectId)">
                      {{ getProjectDetail(row.projectId)?.projectNumber }}
                    </el-tag>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="检查结果" min-width="300">
                <template #default="{ row }">
                  <div class="check-results">
                    <div v-for="item in row.checkItems" :key="item._id" class="check-item">
                      <div class="item-header">
                        <span class="item-name">{{
                          getItemDetail(item.itemId)?.projectName || '未知项目'
                        }}</span>
                        <el-tag
                          size="small"
                          :type="item.conclusion === '正常' ? 'success' : 'warning'"
                        >
                          {{ item.conclusion || '-' }}
                        </el-tag>
                      </div>
                      <div class="item-content">
                        <div class="result-value">
                          检测值：{{ item.result }}
                          <span v-if="getItemDetail(item.itemId)">
                            {{ getItemDetail(item.itemId)?.msrunt }}
                          </span>
                        </div>
                        <div class="standard-range" v-if="getItemDetail(item.itemId)">
                          参考范围：{{ getItemDetail(item.itemId)?.standardValueMin }} -
                          {{ getItemDetail(item.itemId)?.standardValueMax }}
                          {{ getItemDetail(item.itemId)?.msrunt }}
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
            <div class="dept-summary">科室总结：{{ dept.summary }}</div>
          </div>
        </div>
        <div class="detail-section">
          <h3>体检总结</h3>
          <el-descriptions :column="1" border>
            <el-descriptions-item label="健康总结">{{
              currentExamDetail?.healthSummary
            }}</el-descriptions-item>
            <el-descriptions-item label="意见建议">{{
              currentExamDetail?.suggestion
            }}</el-descriptions-item>
            <el-descriptions-item label="职检总结">{{
              currentExamDetail?.jobSummary
            }}</el-descriptions-item>
            <el-descriptions-item label="职检结论">
              <el-tag
                v-for="conclusion in currentExamDetail?.jobConclusion"
                :key="conclusion"
                class="conclusion-tag"
              >
                {{ getConclusionText(conclusion) }}
              </el-tag>
            </el-descriptions-item>
          </el-descriptions>
        </div>
      </div>
    </el-dialog>

    <!-- 历次职业病诊断 -->
    <div v-show="infoSet.diseaseDetermination">
      <div style="width: 100%; height: 10px"></div>
      <div class="titleFlag">
        <div class="tf_icon"></div>
        <div class="tf_text">职业病诊断</div>
        <el-divider border-style="double" style="margin: 0 10px" />
      </div>
      <el-table :data="diseaseDiagnostic" border>
        <!-- 用人单位名称、诊断结论、处理意见、诊断机构、诊断日期 -->
        <el-table-column prop="employerName" label="用人单位名称" />
        <el-table-column prop="diagnosisConclusionDescription" label="诊断结论" />
        <el-table-column prop="treatmentOpinion" label="处理意见" />
        <el-table-column prop="diagnosisInstitution" label="诊断机构" />
        <el-table-column prop="diagnosisDate" label="诊断日期">
          <template #default="{ row }">
            {{ formatDate(row.diagnosisDate) }}
          </template>
        </el-table-column>
      </el-table>
    </div>
    <!-- 历次职业病鉴定 -->
    <div v-show="infoSet.diseaseDetermination">
      <div style="width: 100%; height: 10px"></div>
      <div class="titleFlag">
        <div class="tf_icon"></div>
        <div class="tf_text">职业病鉴定</div>
        <el-divider border-style="double" style="margin: 0 10px" />
      </div>
      <el-table :data="diseaseDetermination" border>
        <el-table-column prop="determinationCategory" label="鉴定类别" />
        <el-table-column prop="applicationReason" label="申请鉴定主要理由" />
        <el-table-column prop="determinationBasis" label="鉴定依据" />
        <el-table-column prop="determinationConclusionDescription" label="鉴定结论" />
        <el-table-column prop="determinationCommittee" label="诊断鉴定委员会" />
      </el-table>
    </div>

    <!-- 历次职业病发生情况 -->
    <div v-show="infoSet.diseaseIncidence">
      <div style="width: 100%; height: 10px"></div>
      <div class="titleFlag">
        <div class="tf_icon"></div>
        <div class="tf_text">职业病发生情况</div>
        <el-divider border-style="double" style="margin: 0 10px" />
      </div>
      <el-table :data="diseaseIncidence" border>
        <el-table-column prop="diagnosisDate" label="诊断日期" width="180">
          <template #default="{ row }">
            {{ formatDate(row.diagnosisDate) }}
          </template>
        </el-table-column>
        <el-table-column prop="diagnosisInstitution" label="诊断机构" width="180" />
        <el-table-column prop="diagnosisConclusionDescription" label="诊断结论" />
        <el-table-column prop="occupationalDisease" label="职业病">
          <template #default="{ row }">
            {{ getArrName(row.occupationalDisease) }}
          </template>
        </el-table-column>
      </el-table>
    </div>

    <div style="display: flex; justify-content: center; margin-top: 1em">
      <el-button plain @click="back">返回</el-button>
      <!-- <el-button type="primary" @click="handleSubmit">保存</el-button> -->
    </div>
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import { useRouter } from 'vue-router'
import {
  employeeRegisterlist,
  getItemList,
  getEmployeeDetail,
  getInfoSetdetail,
  getWorkHistory,
  getDiagnosticRecordList,
  getDeterminationRecordList
} from '@/api/employee'
import {
  ElSelect,
  ElOption,
  ElButton,
  // ElMessage,
  // ElMessageBox,
  ElDivider,
  // ElForm,
  // ElFormItem,
  ElTable,
  ElTableColumn,
  ElInput,
  ElDescriptions,
  ElDescriptionsItem,
  ElDatePicker,
  ElDialog,
  ElTag
  // ElTabs,
  // ElTabPane,
  // ElPopover,
  // ElCascader
} from 'element-plus'
import { ref, onMounted } from 'vue'
import moment from 'moment'

interface CheckItem {
  _id: string
  itemId: string
  result: string
  conclusion: string
}

interface CheckProject {
  _id: string
  projectId: string
  projectName: string
  projectPrice: number
  checkItems: CheckItem[]
}

interface CheckDepartment {
  status: number
  _id: string
  departmentId: string
  departmentName: string
  checkProjects: CheckProject[]
  summary: string
}

interface ExamRecord {
  _id: string
  checkNo: string
  employeeID: string
  EnterpriseID: string
  physicalOrgID: string
  contractID: string
  examPlanID: string
  status: number
  name: string
  gender: string
  idType: string
  IDNum: string
  birthDate: string
  maritalStatus: string
  phone: string
  checkType: string
  hazardFactors: any[]
  checkHazardFactors: any[]
  totalWorkYears: number
  totalWorkMonths: number
  exposureWorkYears: number
  exposureWorkMonths: number
  examType: string
  totalPrice: number
  checkDepartments: CheckDepartment[]
  healthSummary: string
  suggestion: string
  jobSummary: string
  jobConclusion: number[]
  registerTime: string
  createdAt: string
  updatedAt: string
  isRecheck?: boolean
}

import { useRoute } from 'vue-router'
const route = useRoute()

const employeeID = ref<any>(route.query.id)
const infosetID = ref<any>(route.query.infoset)

const router = useRouter()
const formData = ref<any>({
  id: '',
  // 基本信息
  name: '', // 姓名
  idType: '1', // 证件类型
  IDNum: '', // 证件号码
  gender: '', // 性别
  birthDate: '', // 出生日期
  phoneNum: '', // 联系电话
  emergencyContact: '', // 紧急联系人
  emergencyContactNumber: '', // 紧急联系人联系方式
  cname: '', // 用人单位名称
  districtRegAdd: [] // 用人单位所在区域
})

const itemList = ref<any[]>([])

onMounted(async () => {
  const listRes = await getItemList()
  itemList.value = listRes.data || []

  const res = await employeeRegisterlist({ employeeID: employeeID.value })
  // console.log(res)
  const list = res.data?.list || []
  if (Array.isArray(list)) {
    examList.value = list as ExamRecord[]
  } else if (list && typeof list === 'object' && 'data' in list) {
    examList.value = (list as any).data as ExamRecord[]
  }

  const detailRes = await getEmployeeDetail(employeeID.value)
  console.log(detailRes)
  if (detailRes) {
    formData.value = detailRes.data
    formData.value.idType = '1'
    // 根据身份证号计算出生日期
    if (formData.value.IDNum) {
      const birthDate = formData.value.IDNum.slice(6, 14)
      formData.value.birthDate = `${birthDate.slice(0, 4)}-${birthDate.slice(4, 6)}-${birthDate.slice(6, 8)}`
    }
  }

  // 根据信息集id获取信息集详情
  if (infosetID.value) {
    const infoSetRes = await getInfoSetdetail(infosetID.value)
    if (infoSetRes) {
      infoSet.value = infoSetRes.data

      infoSet.value.workHistory && getWorkHistoryData()
      infoSet.value.diseaseDetection && getDiagnosticRecordData()
      infoSet.value.diseaseDetermination && getDeterminationRecordListData()
    }
  }
})

const workExperience = ref([])
const getWorkHistoryData = async () => {
  const res = await getWorkHistory({ employeeID: employeeID.value })
  workExperience.value = res.data?.list || []
}

// 危害检测
const detectList = ref([])
// 体检
const examList = ref<ExamRecord[]>([])
const examDetailVisible = ref(false)
const currentExamDetail = ref<ExamRecord | null>(null)
const showExamDetail = (row) => {
  // 查看体检详情
  currentExamDetail.value = row
  examDetailVisible.value = true
}

// 职业病鉴定
const diseaseDetermination = ref([])
const getDeterminationRecordListData = async () => {
  const res = await getDeterminationRecordList({ idNumber: formData.value.IDNum })
  diseaseDetermination.value = res.data?.list || []
}

// 职业病诊断
const diseaseDiagnostic = ref([])
// 职业病发生情况
const diseaseIncidence = ref([])
const getDiagnosticRecordData = async () => {
  const res = await getDiagnosticRecordList({ idNumber: formData.value.IDNum })
  diseaseDiagnostic.value = res.data?.list || []
  // hasOccupationalDisease为true的记录
  diseaseIncidence.value = diseaseDiagnostic.value.filter(
    (item: any) => item.hasOccupationalDisease
  )
}

// const handleSubmit = async (params?: any) => {
//   console.log(params)
// }

const infoSet = ref({
  workHistory: false, //工作经历信息
  diseaseDetection: false, //历次职业病危害检测信息
  examination: false, //历次健康体检信息
  diseaseDetermination: false, //历次职业病鉴定信息
  diseaseIncidence: false //劳动者职业病发生情况信息
})

const back = () => {
  router.back()
}

//#region getter
const getExamTypeText = (type) => {
  const types = {
    '1': '岗前',
    '2': '在岗',
    '3': '离岗',
    '4': '离岗后',
    '5': '应急'
  }
  return types[type] || type
}

const getProjectDetail = (projectId: string) => {
  return itemList.value.find((item) => item._id === projectId) || null
}

const getItemDetail = (itemId: string) => {
  return itemList.value.find((item) => item._id === itemId) || null
}

const getArrName = (arr) => {
  return arr.map((item) => item.name).join(',')
}

const getJobconclusion = (arr) => {
  let res = ''
  const conclusionMap = {
    '1': '目前未见异常',
    '2': '复查',
    '3': '疑似职业病',
    '4': '禁忌证',
    '5': '其他疾病或异常'
  }
  arr.forEach((item) => {
    res += conclusionMap[item] + ','
  })
}

const getConclusionText = (code) => {
  const conclusionMap = {
    '1': '目前未见异常',
    '2': '复查',
    '3': '疑似职业病',
    '4': '禁忌证',
    '5': '其他疾病或异常'
  }
  return conclusionMap[code] || code
}

const formatDate = (dateStr) => {
  if (!dateStr) return ''
  const date = new Date(dateStr)
  return moment(date).format('YYYY-MM-DD')
}
// #endregion
</script>

<style lang="less">
@import url('@/styles/institutionTitle.less');

.exam-detail {
  .detail-section {
    margin-bottom: 20px;

    h3 {
      margin-bottom: 15px;
      font-size: 16px;
      font-weight: bold;
      color: #303133;
    }

    h4 {
      margin: 15px 0;
      font-size: 15px;
      color: #606266;
    }
  }

  .department {
    padding: 15px;
    margin-bottom: 20px;
    background-color: #f5f7fa;
    border-radius: 4px;

    .dept-summary {
      margin-top: 10px;
      font-style: italic;
      color: #666;
    }
  }

  .project-name {
    display: flex;
    align-items: center;
    gap: 8px;
  }

  .check-results {
    .check-item {
      padding: 8px;
      border-bottom: 1px solid #ebeef5;

      &:last-child {
        border-bottom: none;
      }

      .item-header {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: 4px;

        .item-name {
          font-weight: 500;
          color: #303133;
        }
      }

      .item-content {
        .result-value {
          margin-bottom: 4px;
          font-weight: 500;
          color: #303133;
        }

        .standard-range {
          font-size: 13px;
          color: #909399;
        }
      }
    }
  }

  .conclusion-tag {
    margin-right: 8px;
  }
}
</style>
