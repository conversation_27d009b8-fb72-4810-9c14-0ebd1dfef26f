<template>
  <el-dialog
    title="信息集详情"
    width="600"
    class="detailHeight"
    v-model="showDetailDialog"
    @close="hide"
  >
    <el-descriptions border :column="2">
      <el-descriptions-item label="名称" :span="2">
        {{ formData.name }}
      </el-descriptions-item>
      <el-descriptions-item label="工作经历信息">
        <el-tag v-if="formData.workHistory" type="success">启用</el-tag>
        <el-tag v-else type="info">停用</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="历次职业病危害检测信息">
        <el-tag v-if="formData.diseaseDetection" type="success">启用</el-tag>
        <el-tag v-else type="info">停用</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="历次健康体检信息">
        <el-tag v-if="formData.examination" type="success">启用</el-tag>
        <el-tag v-else type="info">停用</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="历次职业病鉴定信息">
        <el-tag v-if="formData.diseaseDetermination" type="success">启用</el-tag>
        <el-tag v-else type="info">停用</el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="劳动者职业病发生情况信息">
        <el-tag v-if="formData.diseaseIncidence" type="success">启用</el-tag>
        <el-tag v-else type="info">停用</el-tag>
      </el-descriptions-item>
      <el-descriptions-item />
      <el-descriptions-item label="是否启用" :span="2">
        <el-switch v-model="formData.status" disabled />
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-row :gutter="20" class="flexEndClass">
        <el-col :span="24">
          <el-button @click="hide">关闭</el-button>
        </el-col>
      </el-row>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  ElDialog,
  ElRow,
  ElCol,
  ElButton,
  ElDescriptions,
  ElDescriptionsItem,
  ElSwitch,
  ElTag
} from 'element-plus'
import { getInfoSetdetail } from '@/api/employee/index'

// 弹窗是否显示
const showDetailDialog = ref(false)

const getEmptyFormData = () => {
  return {
    id: '',
    name: '',
    workHistory: true,
    diseaseDetection: true,
    examination: true,
    diseaseDetermination: true,
    diseaseIncidence: true,
    status: true
  }
}
const formData = ref(getEmptyFormData())

// 打开
const show = async (id?: string) => {
  if (id) {
    // 获取详情
    const res = await getInfoSetdetail(id)
    formData.value = res.data
  }
  showDetailDialog.value = true
}

// 关闭
const hide = () => {
  showDetailDialog.value = false
  formData.value = getEmptyFormData()
}

// 将方法暴露给父组件
defineExpose({
  show
})
</script>
