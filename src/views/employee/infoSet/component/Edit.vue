<template>
  <el-dialog
    :title="typeNmae + '信息集'"
    width="600"
    class="detailHeight"
    v-model="showDetailDialog"
    @close="hide"
  >
    <el-form :model="formData" label-width="200" ref="formRef" :rules="rules">
      <el-form-item label="名称" prop="name">
        <el-input v-model="formData.name" style="width: 200px" />
      </el-form-item>
      <el-form-item label="工作经历信息">
        <el-radio-group v-model="formData.workHistory">
          <el-radio :value="true">启用</el-radio>
          <el-radio :value="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="历次职业病危害检测信息">
        <el-radio-group v-model="formData.diseaseDetection">
          <el-radio :value="true">启用</el-radio>
          <el-radio :value="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="历次健康体检信息">
        <el-radio-group v-model="formData.examination">
          <el-radio :value="true">启用</el-radio>
          <el-radio :value="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="历次职业病鉴定信息">
        <el-radio-group v-model="formData.diseaseDetermination">
          <el-radio :value="true">启用</el-radio>
          <el-radio :value="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="劳动者职业病发生情况信息">
        <el-radio-group v-model="formData.diseaseIncidence">
          <el-radio :value="true">启用</el-radio>
          <el-radio :value="false">停用</el-radio>
        </el-radio-group>
      </el-form-item>
      <el-form-item label="是否启用">
        <el-switch v-model="formData.status" />
      </el-form-item>
    </el-form>
    <template #footer>
      <el-row :gutter="20" class="flexEndClass">
        <el-col :span="24">
          <el-button @click="hide">取消</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-col>
      </el-row>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ElDialog,
  ElRow,
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElRadio,
  ElRadioGroup,
  ElSwitch,
  ElInput,
  ElMessage
} from 'element-plus'
import { createInfoSet, updateInfoSet, getInfoSetdetail } from '@/api/employee/index'
const emit = defineEmits(['refresh'])

const showDetailDialog = ref(false) // 弹窗是否显示

const dialogType = ref<'add' | 'edit'>('add') // add 新增 | edit 编辑
const typeNmae = computed(() => {
  return dialogType.value === 'add' ? '新增' : '编辑'
})

const formRef = ref<any>(null) // 表单ref
const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
}

// 表单数据初始化
const getEmptyFormData = () => {
  return {
    id: '',
    name: '',
    workHistory: true,
    diseaseDetection: true,
    examination: true,
    diseaseDetermination: true,
    diseaseIncidence: true,
    status: true
  }
}
const formData = ref(getEmptyFormData())

const create = async (data) => {
  const res = await createInfoSet(data)
  if (res.code == 0) {
    ElMessage.success('新增成功')
    // 触发父组件的查询
    emit('refresh')
    hide()
  } else {
    ElMessage.error(res.msg)
  }
}

const update = async (data) => {
  const res = await updateInfoSet(data)
  if (res.code == 0) {
    ElMessage.success('编辑成功')
    // 触发父组件的查询
    emit('refresh')
    hide()
  } else {
    ElMessage.error(res.msg)
  }
}

// 保存
const submit = async () => {
  // 进行表单验证
  await formRef.value
    .validate()
    .then(() => {
      if (dialogType.value === 'add') {
        create(formData.value)
      } else {
        update(formData.value)
      }
    })
    .catch(() => {
      return ''
    })
}

const show = async (type: 'add' | 'edit', id?: string) => {
  dialogType.value = type
  if (id) {
    // 获取详情
    const res = await getInfoSetdetail(id)
    formData.value = res.data
  }
  showDetailDialog.value = true
}

// 关闭
const hide = () => {
  showDetailDialog.value = false
  formData.value = getEmptyFormData()
}

// 将方法暴露给父组件
defineExpose({
  show
})
</script>
