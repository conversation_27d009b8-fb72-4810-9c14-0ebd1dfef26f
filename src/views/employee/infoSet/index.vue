<template>
  <ContentWrap>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">查询条件</div>
      <el-divider border-style="double" />
    </div>
    <div style="display: flex; justify-content: space-between">
      <el-form :inline="true" :model="searchData" class="demo-form-inline" label-width="100px">
        <el-form-item label="信息集名称">
          <el-input v-model="searchData.name" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="search">查询</el-button>
          <!-- <el-button type="warning" plain @click="reset">重置</el-button> -->
        </el-form-item>
      </el-form>
    </div>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">信息集列表</div>
      <el-divider border-style="double" style="margin: 0 10px" />
      <el-button type="primary" @click="editDio('add')">新增</el-button>
      <el-button type="danger" plain @click="deleteMenu()">删除</el-button>
    </div>
    <!-- 表格部分 -->
    <el-table
      style="width: 100%"
      :data="tableData"
      row-key="id"
      border
      ref="tableRef"
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" label="序号" width="60" align="center">
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        tooltip-effect
        label="信息集名称"
        min-width="100"
        :show-overflow-tooltip="true"
      />
      <el-table-column
        prop="workHistory"
        label="工作经历信息"
        :show-overflow-tooltip="true"
        min-width="110"
      >
        <template #default="{ row }">
          <el-tag type="success" v-if="row.workHistory">启用</el-tag>
          <el-tag type="danger" v-else>禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="diseaseDetection"
        label="历次职业病危害检测信息"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          <el-tag type="success" v-if="row.diseaseDetection">启用</el-tag>
          <el-tag type="danger" v-else>禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="examination"
        label="历次健康体检信息"
        :show-overflow-tooltip="true"
        min-width="80"
      >
        <template #default="{ row }">
          <el-tag type="success" v-if="row.examination">启用</el-tag>
          <el-tag type="danger" v-else>禁用</el-tag>
        </template>
      </el-table-column>

      <el-table-column prop="diseaseDetermination" label="历次职业病鉴定信息" min-width="100">
        <template #default="{ row }">
          <el-tag type="success" v-if="row.diseaseDetermination">启用</el-tag>
          <el-tag type="danger" v-else>禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="diseaseIncidence" label="劳动者职业病发生情况信息" min-width="100">
        <template #default="{ row }">
          <el-tag type="success" v-if="row.diseaseIncidence">启用</el-tag>
          <el-tag type="danger" v-else>禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column prop="status" label="是否启用" min-width="80">
        <template #default="{ row }">
          <el-tag type="success" v-if="row.status">启用</el-tag>
          <el-tag type="danger" v-else>禁用</el-tag>
        </template>
      </el-table-column>
      <el-table-column label="操作" min-width="120" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="editDio('edit', scope.row.id)"
            >编辑</el-button
          >
          <el-button type="success" size="small" @click="showDetailDialog(scope.row.id)"
            >详情</el-button
          >
          <!-- <el-button type="danger" plain size="small" @click="deleteMenu(scope.row)">删除</el-button> -->
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top: 20px"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      layout="sizes, prev, pager, next, jumper, total"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <Edit ref="editRef" v-model:dialog-visible.sync="dialogVisible" @refresh="getData" />
    <Detail ref="detailRef" v-model:dialog-visible.sync="dialogVisibleDetail" @refresh="getData" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import Edit from './component/Edit.vue'
import Detail from './component/Detail.vue'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElMessage,
  ElMessageBox,
  ElPagination,
  ElDivider,
  ElForm,
  ElFormItem,
  ElInput,
  ElTag
} from 'element-plus'
import { ref } from 'vue'
import { deleteInfoSet } from '@/api/employee/index'
import { getInfoSetList } from '@/api/employee/index'

const searchData = ref({
  name: ''
})
// 表格数据
const tableData = ref<any>([])
const editRef = ref<any>(null) // 编辑弹窗ref
const detailRef = ref<any>(null) //详情弹窗
const dialogVisible = ref(false) // 编辑弹窗是否显示
const dialogVisibleDetail = ref(false) //详情弹窗是否显示

const getData = async (params?: any) => {
  if (!params) {
    currentPage.value = 1
    pageSize.value = 10

    params = {
      pageNum: currentPage.value,
      pageSize: pageSize.value
    }
  }
  const res = await getInfoSetList(params)
  tableData.value = res.data?.list || []
  total.value = res.data?.total
}

const search = () => {
  currentPage.value = 1
  pageSize.value = 10
  getData({
    ...searchData.value,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
// 重置查询条件
// const reset = () => {
//   searchData.value = {
//     name: ''
//   }
//   currentPage.value = 1
//   pageSize.value = 10
//   getData({
//     pageNum: currentPage.value,
//     pageSize: pageSize.value
//   })
// }

const tableRef = ref()

const deleteMenu = () => {
  const seleted = tableRef.value.getSelectionRows()
  if (seleted.length === 0) {
    return ElMessage.error('请至少选择一项需要删除的信息集！')
  }
  const ids = seleted.map((item) => item.id)
  ElMessageBox.confirm(`确认删除所选信息集吗?`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(async () => {
      const res = await deleteInfoSet(ids)
      if (res.code == 0) {
        getData()
        ElMessage.success('删除成功')
      } else {
        ElMessage.error(res.msg || '删除失败')
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      })
    })
}

// #region 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
// #endregion

const editDio = (type: 'add' | 'edit' | 'view', id?: string) => {
  if (editRef.value) {
    editRef.value.show(type, id)
  }
}

const showDetailDialog = (id: string) => {
  if (detailRef.value) {
    detailRef.value.show(id)
  }
}

getData({ page: currentPage.value, pageSize: pageSize.value })
</script>

<style lang="less">
@import url('@/styles/institutionTitle.less');
</style>
