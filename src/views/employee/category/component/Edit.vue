<template>
  <el-dialog
    :title="typeNmae + '信息集'"
    width="600"
    class="detailHeight"
    v-model="showDetailDialog"
    @close="hide"
  >
    <el-form :model="formData" label-width="200" ref="formRef" :rules="rules">
      <el-form-item label="类别名称">
        <el-input v-model="formData.name" />
      </el-form-item>
      <el-form-item label="接触危害因素">
        <el-input v-model="formData.harmfactor" />
      </el-form-item>
      <el-form-item label="所在单位">
        <el-input v-model="formData.enterprise" />
      </el-form-item>
      <el-form-item label="单位区域">
        <el-input v-model="formData.area" />
      </el-form-item>
      <el-form-item label="单位行业">
        <el-input v-model="formData.industry" />
      </el-form-item>
      <el-form-item label="是否启用">
        <!-- <el-switch v-model="formData.state" /> -->
        <el-radio-group v-model="formData.state">
          <el-radio :value="1">启用</el-radio>
          <el-radio :value="0">停用</el-radio>
        </el-radio-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <el-row :gutter="20" class="flexEndClass">
        <el-col :span="24">
          <el-button @click="hide">取消</el-button>
          <el-button type="primary" @click="submit">保存</el-button>
        </el-col>
      </el-row>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref, computed } from 'vue'
import {
  ElDialog,
  ElRow,
  ElCol,
  ElButton,
  ElForm,
  ElFormItem,
  ElInput,
  // ElSelect,
  ElRadio,
  ElRadioGroup,
  // ElOption,
  // ElCascader,
  // ElSwitch,
  ElMessage
} from 'element-plus'

import {
  getEmployeeCategoryDetail,
  createEmployeeCategory,
  updateEmployeeCategory
} from '@/api/employee'

const showDetailDialog = ref(false) // 弹窗是否显示
const dialogType = ref<'add' | 'edit'>('add') // 弹窗类型
const typeNmae = computed(() => {
  return dialogType.value === 'add' ? '新增' : '编辑'
})

const rules = {
  name: [{ required: true, message: '请输入名称', trigger: 'blur' }]
}

const formData = ref({
  id: '',
  name: '',
  harmfactor: '',
  enterprise: '',
  area: '',
  industry: '',
  state: true
})

// 重置表单
const resetForm = () => {
  formData.value = {
    id: '',
    name: '',
    harmfactor: '',
    enterprise: '',
    area: '',
    industry: '',
    state: true
  }
}

const formRef = ref<any>(null) // 表单ref

const create = async (data) => {
  const res = await createEmployeeCategory(data)
  if (res.code == 0) {
    ElMessage.success('新增成功')
    // 触发父组件的查询
    emit('refresh')
    hide()
  } else {
    ElMessage.error(res.msg)
  }
}

const update = async (data) => {
  const res = await updateEmployeeCategory(formData.value.id, data)
  if (res.code == 0) {
    ElMessage.success('编辑成功')
    // 触发父组件的查询
    emit('refresh')
    hide()
  } else {
    ElMessage.error(res.msg)
  }
}

// 保存
const submit = async () => {
  // 进行表单验证
  await formRef.value
    .validate()
    .then(() => {
      if (dialogType.value === 'add') {
        create(formData.value)
      } else {
        update(formData.value)
      }
    })
    .catch(() => {
      return ''
    })
}

const show = async (type: 'add' | 'edit', id?: string) => {
  dialogType.value = type
  if (id) {
    // 获取详情
    const res = await getEmployeeCategoryDetail(id)
    formData.value = res.data
  }
  showDetailDialog.value = true
}

// 关闭
const hide = () => {
  showDetailDialog.value = false
  resetForm()
}

const emit = defineEmits(['refresh'])

// 将方法暴露给父组件
defineExpose({
  show
})
</script>
