<template>
  <el-dialog
    title="编辑/新增信息集"
    width="600"
    class="detailHeight"
    v-model="showDetailDialog"
    @close="hide"
  >
    <el-descriptions :model="formData" label-width="120" border :column="2">
      <el-descriptions-item label="类别名称">
        {{ formData.name }}
      </el-descriptions-item>
      <el-descriptions-item label="接触危害因素">
        {{ formData.harmfactor }}
      </el-descriptions-item>
      <el-descriptions-item label="所在单位">
        {{ formData.enterprise }}
      </el-descriptions-item>
      <el-descriptions-item label="单位区域">
        {{ formData.area }}
      </el-descriptions-item>
      <el-descriptions-item label="单位行业">
        {{ formData.industry }}
      </el-descriptions-item>
      <el-descriptions-item />
      <el-descriptions-item label="是否启用" :span="2">
        <el-switch v-model="formData.status" disabled />
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <el-row :gutter="20" class="flexEndClass">
        <el-col :span="24">
          <el-button @click="hide">关闭</el-button>
        </el-col>
      </el-row>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
import { ref } from 'vue'
import {
  ElDialog,
  ElRow,
  ElCol,
  ElButton,
  // ElInput,
  ElDescriptions,
  ElDescriptionsItem,
  ElSwitch
} from 'element-plus'

// 定义props
const props = defineProps({
  getCategory: {
    type: Function,
    required: true
  }
})

const showDetailDialog = ref(false) // 弹窗是否显示
const employeeId = ref('') // 需要查看的类别id

const formData = ref({
  id: '',
  name: '',
  harmfactor: '',
  enterprise: '',
  area: '',
  industry: '',
  status: true
})

const show = (id: string) => {
  showDetailDialog.value = true
  employeeId.value = id

  // 从父组件获取数据
  const categoryData = props.getCategory(id)
  if (categoryData) {
    formData.value = {
      ...categoryData,
      status: categoryData.state === 1
    }
  }
}

// 关闭
const hide = () => {
  showDetailDialog.value = false
  formData.value = {
    id: '',
    name: '',
    harmfactor: '',
    enterprise: '',
    area: '',
    industry: '',
    status: true
  }
}

// 将方法暴露给父组件
defineExpose({
  show
})
</script>
