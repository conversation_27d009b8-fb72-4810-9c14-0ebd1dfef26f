<script setup lang="ts">
import { ref } from 'vue'
import { ElDialog, ElRow, ElCol, ElButton } from 'element-plus'
import { getInfoSetList } from '@/api/employee'

import { useRouter } from 'vue-router'
const router = useRouter()

const showDetailDialog = ref(false) // 弹窗是否显示

const employeeId = ref('') // 需要查看的人员id
const infoSetList = ref<
  {
    id: number
    name: string
    workHistory: boolean
    diseaseDetection: boolean
    examination: boolean
    diseaseDetermination: boolean
    diseaseIncidence: boolean
  }[]
>([])

const getData = async () => {
  const res = await getInfoSetList({ status: 1 })
  infoSetList.value = res.data?.list || []
}
getData()

const show = (id: string) => {
  showDetailDialog.value = true
  employeeId.value = id
}

// 关闭
const hide = () => {
  showDetailDialog.value = false
}

const goDetail = (infoset) => {
  console.log('goDetail', employeeId.value)
  router.push({
    path: '/employee/detail',
    query: { id: employeeId.value, infoset }
  })
}
// 将方法暴露给父组件
defineExpose({
  show
})
</script>

<template>
  <el-dialog
    title="选择信息集"
    width="60%"
    class="detailHeight"
    v-model="showDetailDialog"
    @close="hide"
  >
    <div style="display: flex; width: 100%; justify-content: center; flex-wrap: wrap">
      <div style="display: flex; flex-wrap: wrap">
        <el-button
          type="primary"
          @click="goDetail(item.id)"
          v-for="item in infoSetList"
          :key="item.id"
          >{{ item.name }}</el-button
        >
      </div>
    </div>
    <template #footer>
      <el-row :gutter="20" class="flexEndClass">
        <el-col :span="24">
          <el-button @click="hide">取消</el-button>
        </el-col>
      </el-row>
    </template>
  </el-dialog>
</template>
