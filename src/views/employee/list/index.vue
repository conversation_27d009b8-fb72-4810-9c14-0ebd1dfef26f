<template>
  <ContentWrap>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">查询条件</div>
      <el-divider border-style="double" />
    </div>
    <el-form :inline="true" :model="searchData" class="demo-form-inline" label-width="100px">
      <el-form-item label="姓名">
        <el-input v-model="searchData.name" placeholder="请输入" clearable />
      </el-form-item>
      <el-form-item label="类别">
        <el-select
          v-model="searchData.category"
          placeholder="请选择"
          style="width: 240px"
          clearable
        >
          <el-option
            v-for="item in options"
            :key="item.value"
            :label="item.label"
            :value="item.value"
          />
        </el-select>
      </el-form-item>
      <el-form-item>
        <el-button type="primary" plain @click="search">查询</el-button>
        <el-button type="warning" plain @click="reset">重置</el-button>
      </el-form-item>
    </el-form>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">劳动者列表</div>
      <el-divider border-style="double" style="margin: 0 10px" />
      <!-- <el-button type="primary" @click="editDio('add')">新增</el-button> -->
      <!-- <el-button type="danger" plain @click="deleteMenu()">删除</el-button> -->
    </div>
    <!-- 表格部分 -->
    <el-table
      style="width: 100%"
      :data="tableData"
      row-key="id"
      border
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <!-- <el-table-column type="selection" width="55" /> -->
      <el-table-column type="index" label="序号" width="60" align="center">
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="name"
        tooltip-effect
        label="姓名"
        min-width="100"
        :show-overflow-tooltip="true"
      />
      <el-table-column prop="gender" label="性别" :show-overflow-tooltip="true" min-width="80">
        <template #default="scope">
          {{ scope.row.gender === '0' ? '男' : scope.row.gender === '1' ? '女' : '' }}
        </template>
      </el-table-column>
      <el-table-column prop="IDNum" label="身份证" :show-overflow-tooltip="true" min-width="100" />
      <el-table-column
        prop="phoneNum"
        label="手机号"
        :show-overflow-tooltip="true"
        min-width="100"
      />
      <el-table-column
        prop="enterpriseName"
        label="用人单位"
        :show-overflow-tooltip="true"
        min-width="120"
      >
        <template #default="scope">
          {{ scope.row.EnterpriseID?.cname }}
        </template>
      </el-table-column>
      <el-table-column
        prop="districts"
        label="用人单位所在区域"
        :show-overflow-tooltip="true"
        min-width="120"
      >
        <template #default="scope">
          {{ scope.row.EnterpriseID?.districtRegAdd.join('/') }}
        </template>
      </el-table-column>
      <el-table-column
        prop="industryCategory"
        label="行业"
        :show-overflow-tooltip="true"
        min-width="120"
      >
        <template #default="scope">
          {{
            (scope.row.EnterpriseID.industryCategory &&
              scope.row.EnterpriseID.industryCategory.length >= 1 &&
              getIndustryCategoryName(scope.row.EnterpriseID.industryCategory[0])) ||
            ''
          }}
        </template>
      </el-table-column>
      <!-- <el-table-column prop="harmCategory" label="接触危害因素分类" :show-overflow-tooltip="true" min-width="80">
      </el-table-column> -->
      <el-table-column label="操作" min-width="160" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="showDetailDialog(scope.row._id)"
            >编辑</el-button
          >
          <el-button type="success" size="small" @click="showDetailDialog(scope.row._id)"
            >详情</el-button
          >
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top: 20px"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      layout="sizes, prev, pager, next, jumper, total"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <selectInfoSetDialog ref="detailRef" v-model:dialog-visible.sync="dialogVisibleDetail" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { getIndustryCategory } from '@/api/common'

import { ContentWrap } from '@/components/ContentWrap'
import selectInfoSetDialog from './component/selectInfoSetDialog.vue'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  // ElMessage,
  // ElMessageBox,
  ElPagination,
  ElDivider,
  ElForm,
  ElFormItem,
  ElInput,
  ElSelect,
  ElOption
} from 'element-plus'

import { getEmployeeList, getEmployeeCategoryList } from '@/api/employee'

import { ref } from 'vue'
const searchData = ref({
  name: '',
  category: ''
})
const tableData = ref<any>([
  // {
  //   id: 1,
  //   name: 'lcz',
  //   gender: '0',
  //   IDNum: '123456789',
  //   phoneNum: '123456789',
  //   enterpriseName: '企业1',
  //   districts: '区域1',
  //   industryCategory: '行业1',
  //   harmCategory: '危害因素1'
  // }
]) // 表格
const detailRef = ref<any>(null) //详情弹窗
const dialogVisibleDetail = ref(false) //详情弹窗是否显示

const getData = async (params?: any) => {
  const res = await getEmployeeList(params)
  console.log(params)
  tableData.value = res.data?.list || []
  total.value = res.data?.total

  const res3 = await getEmployeeCategoryList({ pageNum: 1, pageSize: 1000, state: 1 })
  options.value =
    res3.data?.list.map((item: any) => {
      return {
        value: item.id,
        label: item.name
      }
    }) || []
}
const search = () => {
  currentPage.value = 1
  pageSize.value = 10
  getData({
    ...searchData.value,
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
// 重置查询条件
const reset = () => {
  searchData.value = {
    name: '',
    category: ''
  }
  currentPage.value = 1
  pageSize.value = 10
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}

// #region 分页
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getData({
    pageNum: currentPage.value,
    pageSize: pageSize.value
  })
}
// #endregion

const showDetailDialog = (id: string) => {
  if (detailRef.value) {
    detailRef.value.show(id)
  }
}

getData({ pageNum: currentPage.value, pageSize: pageSize.value })

const industryCategoryOptions = ref<any[]>([])

const getindustryCategoryOptions = async () => {
  const response = await getIndustryCategory()
  industryCategoryOptions.value = response.data
}

const getIndustryCategoryName = (idArr: string[]) => {
  // 最高四级树结构
  const industryCategory = industryCategoryOptions.value
  let name = ''
  const getName = (arr: any[], idArr: string[]) => {
    for (let i = 0; i < arr.length; i++) {
      const item = arr[i]
      if (item.value === idArr[0]) {
        name += item.label
        if (idArr.length > 1) {
          getName(item.children, idArr.slice(1))
        }
        break
      }
    }
  }
  idArr.length >= 1 && getName(industryCategory, idArr)
  return name
}

// 初始化获取行业类别数据
getindustryCategoryOptions()

const options = ref<any>([])
</script>

<style lang="less">
@import url('@/styles/institutionTitle.less');
</style>
