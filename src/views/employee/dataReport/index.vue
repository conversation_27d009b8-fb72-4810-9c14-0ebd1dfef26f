<template>
  <ContentWrap>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">查询条件</div>
      <el-divider border-style="double" />
    </div>
    <div style="display: flex; justify-content: space-between">
      <el-form :inline="true" :model="searchData" class="demo-form-inline" label-width="100px">
        <el-form-item label="机构名称">
          <el-input v-model="searchData.orgName" placeholder="请输入机构名称" clearable />
        </el-form-item>
        <el-form-item>
          <el-button type="primary" plain @click="search">查询</el-button>
        </el-form-item>
      </el-form>
    </div>

    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">机构列表</div>
      <el-divider border-style="double" style="margin: 0 10px" />
      <el-button type="primary" @click="editDio('add')">新增机构</el-button>
    </div>
    <!-- 表格部分 -->
    <el-table
      style="width: 100%"
      :data="tableData"
      row-key="id"
      border
      ref="tableRef"
      @selection-change="handleSelectionChange"
      :header-cell-style="{ backgroundColor: '#f5f7fa', color: '#333333' }"
    >
      <el-table-column type="selection" width="55" />
      <el-table-column type="index" label="序号" width="60" align="center">
        <template #default="scope">
          {{ (currentPage - 1) * pageSize + scope.$index + 1 }}
        </template>
      </el-table-column>
      <el-table-column
        prop="orgName"
        label="机构名称"
        :show-overflow-tooltip="true"
        min-width="180"
      />
      <el-table-column prop="orgType" label="机构类型" min-width="150">
        <template #default="{ row }">
          <el-tag :type="getOrgTypeTagType(row.orgType)">
            {{ row.orgType }}
          </el-tag>
        </template>
      </el-table-column>
      <el-table-column
        prop="creditCode"
        label="统一社会信用代码"
        :show-overflow-tooltip="true"
        min-width="180"
      />
      <el-table-column
        prop="permissions"
        label="机构上报权限"
        :show-overflow-tooltip="true"
        min-width="200"
      />
      <el-table-column label="操作" min-width="180" fixed="right">
        <template #default="scope">
          <el-button type="primary" size="small" @click="detailDio(scope.row.id)"
            >查看详情</el-button
          >
          <el-button type="primary" size="small" @click="editDio('edit', scope.row.id)"
            >编辑</el-button
          >
          <el-button type="danger" size="small" @click="deleteItem(scope.row.id)">删除</el-button>
        </template>
      </el-table-column>
    </el-table>

    <el-pagination
      style="margin-top: 20px"
      v-model:current-page="currentPage"
      v-model:page-size="pageSize"
      :page-sizes="[10, 20, 30, 50, 100]"
      layout="sizes, prev, pager, next, jumper, total"
      :total="total"
      @size-change="handleSizeChange"
      @current-change="handleCurrentChange"
    />

    <Edit
      ref="editRef"
      v-model:dialogVisible="dialogVisible"
      :get-data="getDataById"
      @refresh="getData"
    />
    <Detail ref="detailRef" v-model:dialogVisible="dialogVisibleDetail" :get-data="getDataById" />
  </ContentWrap>
</template>

<script lang="ts" setup>
import { ContentWrap } from '@/components/ContentWrap'
import Edit from './component/Edit.vue'
import Detail from './component/Detail.vue'
import {
  ElTable,
  ElTableColumn,
  ElButton,
  ElMessage,
  ElMessageBox,
  ElPagination,
  ElDivider,
  ElForm,
  ElFormItem,
  ElInput,
  ElTag
  // ElSelect,
  // ElOption
} from 'element-plus'
import { ref, onMounted } from 'vue'

const STORAGE_KEY = 'organization_list'

const searchData = ref({
  orgName: ''
})

const tableData = ref<any>([])
const selectedRows = ref<any>([])

// 获取数据
const getData = () => {
  const data = localStorage.getItem(STORAGE_KEY)
  if (data) {
    const allData = JSON.parse(data)
    // 过滤和分页
    const filteredData = allData.filter((item: any) => {
      if (searchData.value.orgName && !item.orgName.includes(searchData.value.orgName)) return false
      return true
    })
    total.value = filteredData.length
    const start = (currentPage.value - 1) * pageSize.value
    const end = start + pageSize.value
    tableData.value = filteredData.slice(start, end)
  } else {
    // 添加mock数据
    const mockData = [
      {
        id: '1',
        orgName: '新疆维吾尔自治区职业病防治院',
        orgType: '职业卫生技术服务机构',
        creditCode: '91650102MA78REXH2A',
        permissions: '体检、诊断'
      },
      {
        id: '2',
        orgName: '乌鲁木齐市第一人民医院',
        orgType: '医疗机构',
        creditCode: '12650102MB0C2D43XN',
        permissions: '医疗、职业病康复治疗'
      },
      {
        id: '3',
        orgName: '新疆生产建设兵团第一师医院',
        orgType: '职业病诊断机构',
        creditCode: '91659001MA7776XH1B',
        permissions: '诊断、医疗'
      },
      {
        id: '4',
        orgName: '新疆维吾尔自治区人民医院',
        orgType: '职业病健康检查机构',
        creditCode: '12650000456789012X',
        permissions: '体检、医疗'
      },
      {
        id: '5',
        orgName: '新疆医科大学第一附属医院',
        orgType: '职业病鉴定机构',
        creditCode: '91650102562103847J',
        permissions: '鉴定、医疗'
      },
      {
        id: '6',
        orgName: '新疆维吾尔自治区肿瘤医院',
        orgType: '放射性卫生服务机构',
        creditCode: '91650102MA78TY5X1C',
        permissions: '医疗、体检'
      },
      {
        id: '7',
        orgName: '乌鲁木齐市第四人民医院',
        orgType: '医疗机构',
        creditCode: '91650102MA78REXH2B',
        permissions: '医疗'
      },
      {
        id: '8',
        orgName: '新疆维吾尔自治区中医院',
        orgType: '职业病康复治疗机构',
        creditCode: '91650102MA78REXH2C',
        permissions: '职业病康复治疗'
      }
    ]
    localStorage.setItem(STORAGE_KEY, JSON.stringify(mockData))
    total.value = mockData.length
    tableData.value = mockData.slice(0, pageSize.value)
  }
}

// 搜索
const search = () => {
  currentPage.value = 1
  getData()
}

const tableRef = ref()

// 删除单个项目
const deleteItem = (id: string) => {
  ElMessageBox.confirm(`确认删除该机构吗?`, '提示', {
    confirmButtonText: '确认',
    cancelButtonText: '取消',
    type: 'warning'
  })
    .then(() => {
      const data = localStorage.getItem(STORAGE_KEY)
      if (data) {
        const allData = JSON.parse(data)
        const newData = allData.filter((item: any) => item.id !== id)
        localStorage.setItem(STORAGE_KEY, JSON.stringify(newData))
        getData()
        ElMessage.success('删除成功')
      }
    })
    .catch(() => {
      ElMessage({
        type: 'info',
        message: '已取消删除'
      })
    })
}

// 分页相关
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const handleSizeChange = (val) => {
  pageSize.value = val
  currentPage.value = 1
  getData()
}
const handleCurrentChange = (val) => {
  currentPage.value = val
  getData()
}

const editRef = ref<any>(null)
const detailRef = ref<any>(null)
const dialogVisible = ref(false)
const dialogVisibleDetail = ref(false)

const editDio = (type: 'add' | 'edit' | 'view', id?: string) => {
  if (editRef.value) {
    editRef.value.show(type, id)
  }
}

// 获取机构类型对应的标签类型
const getOrgTypeTagType = (type: string) => {
  const typeMap = {
    职业卫生技术服务机构: 'success',
    职业病健康检查机构: 'warning',
    职业病诊断机构: 'danger',
    职业病鉴定机构: 'info',
    放射性卫生服务机构: 'primary',
    医疗机构: '',
    职业病康复治疗机构: 'success'
  }
  return typeMap[type] || ''
}

const detailDio = (id: string) => {
  if (detailRef.value) {
    detailRef.value.show(id)
  }
}

// 表格选择
const handleSelectionChange = (selection) => {
  selectedRows.value = selection
}

// 获取单个数据
const getDataById = (id: string) => {
  const data = localStorage.getItem(STORAGE_KEY)
  if (data) {
    const allData = JSON.parse(data)
    return allData.find((item) => item.id === id)
  }
  return null
}

onMounted(() => {
  getData()
})
</script>

<style lang="less">
@import url('@/styles/institutionTitle.less');
</style>
