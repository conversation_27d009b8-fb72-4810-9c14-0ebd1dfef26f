<template>
  <el-dialog
    title="机构详情"
    :model-value="dialogVisible"
    @update:model-value="emit('update:dialogVisible', $event)"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item label="机构名称">{{ detailData.orgName }}</el-descriptions-item>
      <el-descriptions-item label="机构类型">
        <template v-if="detailData.orgType && detailData.orgType.includes('、')">
          <div
            v-for="(type, index) in detailData.orgType.split('、')"
            :key="index"
            style="margin: 2px 0"
          >
            <el-tag :type="getOrgTypeTagType(type)">
              {{ type }}
            </el-tag>
          </div>
        </template>
        <template v-else>
          <el-tag :type="getOrgTypeTagType(detailData.orgType)">
            {{ detailData.orgType }}
          </el-tag>
        </template>
      </el-descriptions-item>
      <el-descriptions-item label="统一社会信用代码">{{
        detailData.creditCode
      }}</el-descriptions-item>
      <el-descriptions-item label="机构上报权限">
        <template v-if="detailData.permissions && detailData.permissions.includes('、')">
          <el-tag
            v-for="(permission, index) in detailData.permissions.split('、')"
            :key="index"
            type="success"
            style="margin: 2px 4px 2px 0"
          >
            {{ permission }}
          </el-tag>
        </template>
        <template v-else>
          <el-tag type="success">{{ detailData.permissions }}</el-tag>
        </template>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="emit('update:dialogVisible', false)">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElDialog, ElDescriptions, ElDescriptionsItem, ElTag, ElButton } from 'element-plus'
import { reactive } from 'vue'

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  getData: {
    type: Function,
    default: () => {}
  }
})

const emit = defineEmits(['update:dialogVisible'])

const detailData = reactive({
  orgName: '',
  orgType: '',
  creditCode: '',
  permissions: ''
})

// 获取机构类型对应的标签类型
const getOrgTypeTagType = (type: string) => {
  const typeMap = {
    职业卫生技术服务机构: 'success',
    职业病健康检查机构: 'warning',
    职业病诊断机构: 'danger',
    职业病鉴定机构: 'info',
    放射性卫生服务机构: 'primary',
    医疗机构: '',
    职业病康复治疗机构: 'success'
  }
  return typeMap[type] || ''
}

const show = (id: string) => {
  const data = props.getData(id)
  if (data) {
    Object.assign(detailData, data)
  }
  emit('update:dialogVisible', true)
}

defineExpose({
  show
})
</script>
