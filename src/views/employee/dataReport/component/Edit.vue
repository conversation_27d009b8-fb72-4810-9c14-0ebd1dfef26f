<template>
  <el-dialog
    :title="dialogType === 'add' ? '新增机构' : '编辑机构'"
    :model-value="dialogVisible"
    @update:model-value="emit('update:dialogVisible', $event)"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="机构名称" prop="orgName">
        <el-input
          v-model="formData.orgName"
          placeholder="请输入机构名称"
          :disabled="dialogType === 'edit'"
        />
      </el-form-item>
      <el-form-item label="机构类型" prop="orgType">
        <el-select
          v-model="selectedOrgTypes"
          multiple
          placeholder="请选择机构类型"
          class="w-full"
          :disabled="dialogType === 'edit'"
        >
          <el-option label="职业卫生技术服务机构" value="职业卫生技术服务机构" />
          <el-option label="职业病健康检查机构" value="职业病健康检查机构" />
          <el-option label="职业病诊断机构" value="职业病诊断机构" />
          <el-option label="职业病鉴定机构" value="职业病鉴定机构" />
          <el-option label="放射性卫生服务机构" value="放射性卫生服务机构" />
          <el-option label="医疗机构" value="医疗机构" />
          <el-option label="职业病康复治疗机构" value="职业病康复治疗机构" />
        </el-select>
      </el-form-item>
      <el-form-item label="社会信用代码" prop="creditCode">
        <el-input
          v-model="formData.creditCode"
          placeholder="请输入统一社会信用代码"
          :disabled="dialogType === 'edit'"
        />
      </el-form-item>
      <el-form-item label="机构上报权限" prop="permissions">
        <el-checkbox-group v-model="selectedPermissions">
          <el-checkbox label="体检">体检</el-checkbox>
          <el-checkbox label="诊断">诊断</el-checkbox>
          <el-checkbox label="鉴定">鉴定</el-checkbox>
          <el-checkbox label="医疗">医疗</el-checkbox>
          <el-checkbox label="职业病康复治疗">职业病康复治疗</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="emit('update:dialogVisible', false)">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElCheckboxGroup,
  ElCheckbox
} from 'element-plus'
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  getData: {
    type: Function,
    default: () => {}
  }
})

const emit = defineEmits(['update:dialogVisible', 'refresh'])

const STORAGE_KEY = 'organization_list'

const formRef = ref<FormInstance>()
const dialogType = ref<'add' | 'edit' | 'view'>('add')

interface OrganizationData {
  id?: string
  orgName: string
  orgType: string
  creditCode: string
  permissions: string
}

const formData = reactive<OrganizationData>({
  orgName: '',
  orgType: '',
  creditCode: '',
  permissions: ''
})

const selectedPermissions = ref<string[]>([])
const selectedOrgTypes = ref<string[]>([])

// 监听选中权限变化
watch(selectedPermissions, (newVal) => {
  formData.permissions = newVal.join('、')
})

// 监听选中机构类型变化
watch(selectedOrgTypes, (newVal) => {
  formData.orgType = newVal.join('、')
})

const rules = {
  orgName: [{ required: true, message: '请输入机构名称', trigger: 'blur' }],
  orgType: [{ required: true, message: '请选择机构类型', trigger: 'change' }],
  creditCode: [{ required: true, message: '请输入统一社会信用代码', trigger: 'blur' }],
  permissions: [{ required: true, message: '请选择机构上报权限', trigger: 'change' }]
}

const show = (type: 'add' | 'edit' | 'view', id?: string) => {
  dialogType.value = type
  if (type === 'add') {
    formData.orgName = ''
    formData.orgType = ''
    formData.creditCode = ''
    formData.permissions = ''
    selectedPermissions.value = []
    selectedOrgTypes.value = []
  } else if (id) {
    const data = props.getData(id)
    if (data) {
      formData.id = data.id
      formData.orgName = data.orgName
      formData.orgType = data.orgType
      formData.creditCode = data.creditCode
      formData.permissions = data.permissions
      selectedPermissions.value = data.permissions ? data.permissions.split('、') : []
      selectedOrgTypes.value = data.orgType ? data.orgType.split('、') : []
    }
  }
  emit('update:dialogVisible', true)
}

const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      const data = localStorage.getItem(STORAGE_KEY)
      const newData = {
        ...formData
      }
      if (data) {
        const allData = JSON.parse(data)
        if (dialogType.value === 'add') {
          allData.push({
            ...newData,
            id: Date.now().toString()
          })
        } else {
          const index = allData.findIndex((item: any) => item.id === formData.id)
          if (index !== -1) {
            allData[index] = newData
          }
        }
        localStorage.setItem(STORAGE_KEY, JSON.stringify(allData))
      } else {
        localStorage.setItem(
          STORAGE_KEY,
          JSON.stringify([
            {
              ...newData,
              id: Date.now().toString()
            }
          ])
        )
      }
      emit('refresh')
      emit('update:dialogVisible', false)
      ElMessage.success(dialogType.value === 'add' ? '新增成功' : '编辑成功')
    }
  })
}

defineExpose({
  show
})
</script>
