<template>
  <el-dialog
    title="申请详情"
    :model-value="dialogVisible"
    @update:model-value="emit('update:dialogVisible', $event)"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-descriptions :column="1" border>
      <el-descriptions-item label="姓名">{{ detailData.name }}</el-descriptions-item>
      <el-descriptions-item label="单位名称">{{ detailData.unitName }}</el-descriptions-item>
      <el-descriptions-item label="所属部门">{{ detailData.department }}</el-descriptions-item>
      <el-descriptions-item label="职位">{{ detailData.position }}</el-descriptions-item>
      <el-descriptions-item label="申请类型">
        <el-tag :type="detailData.permissionType === '全部' ? 'danger' : 'warning'">
          {{ detailData.permissionType }}
        </el-tag>
      </el-descriptions-item>
      <el-descriptions-item label="申请项目">{{ detailData.permissionItems }}</el-descriptions-item>
      <el-descriptions-item label="状态">
        <el-tag
          :type="
            detailData.status === 'approved'
              ? 'success'
              : detailData.status === 'pending'
                ? 'warning'
                : 'danger'
          "
        >
          {{
            detailData.status === 'approved'
              ? '已通过'
              : detailData.status === 'pending'
                ? '待审批'
                : '已拒绝'
          }}
        </el-tag>
      </el-descriptions-item>
    </el-descriptions>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="emit('update:dialogVisible', false)">关闭</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import { ElDialog, ElDescriptions, ElDescriptionsItem, ElTag, ElButton } from 'element-plus'
import { reactive } from 'vue'

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  getData: {
    type: Function,
    default: () => {}
  }
})

const emit = defineEmits(['update:dialogVisible'])

const detailData = reactive({
  name: '',
  unitName: '',
  department: '',
  position: '',
  permissionType: '',
  permissionItems: '',
  status: ''
})

const show = (id: string) => {
  const data = props.getData(id)
  if (data) {
    Object.assign(detailData, data)
  }
  emit('update:dialogVisible', true)
}

defineExpose({
  show
})
</script>
