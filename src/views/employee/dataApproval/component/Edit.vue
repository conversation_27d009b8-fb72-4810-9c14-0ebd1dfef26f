<template>
  <el-dialog
    :title="dialogType === 'add' ? '新增申请' : '编辑申请'"
    :model-value="dialogVisible"
    @update:model-value="emit('update:dialogVisible', $event)"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-form
      ref="formRef"
      :model="formData"
      :rules="rules"
      label-width="100px"
      class="demo-ruleForm"
    >
      <el-form-item label="姓名" prop="name">
        <el-input v-model="formData.name" placeholder="请输入姓名" />
      </el-form-item>
      <el-form-item label="单位名称" prop="unitName">
        <el-input v-model="formData.unitName" placeholder="请输入单位名称" />
      </el-form-item>
      <el-form-item label="所属部门" prop="department">
        <el-select v-model="formData.department" placeholder="请选择部门" class="w-full">
          <el-option v-for="dept in departments" :key="dept" :label="dept" :value="dept" />
        </el-select>
      </el-form-item>
      <el-form-item label="职位" prop="position">
        <el-input v-model="formData.position" placeholder="请输入职位" />
      </el-form-item>
      <el-form-item label="申请类型" prop="permissionType">
        <el-select v-model="formData.permissionType" placeholder="请选择申请类型" class="w-full">
          <el-option label="全部" value="全部" />
          <el-option label="部分" value="部分" />
        </el-select>
      </el-form-item>
      <el-form-item label="申请项目" prop="permissionItems">
        <el-checkbox-group v-model="selectedItems">
          <el-checkbox label="身份证号">身份证号</el-checkbox>
          <el-checkbox label="手机号">手机号</el-checkbox>
          <el-checkbox label="银行卡号">银行卡号</el-checkbox>
          <el-checkbox label="邮箱">邮箱</el-checkbox>
        </el-checkbox-group>
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button @click="emit('update:dialogVisible', false)">取消</el-button>
        <el-button type="primary" @click="submitForm">确定</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script lang="ts" setup>
import {
  ElDialog,
  ElForm,
  ElFormItem,
  ElInput,
  ElButton,
  ElSelect,
  ElOption,
  ElCheckboxGroup,
  ElCheckbox
} from 'element-plus'
import { ref, reactive, watch } from 'vue'
import { ElMessage } from 'element-plus'
import type { FormInstance } from 'element-plus'

const props = defineProps({
  dialogVisible: {
    type: Boolean,
    default: false
  },
  getData: {
    type: Function,
    default: () => {}
  }
})

const emit = defineEmits(['update:dialogVisible', 'refresh'])

const STORAGE_KEY = 'sensitive_data_permission'

const formRef = ref<FormInstance>()
const dialogType = ref<'add' | 'edit'>('add')

const departments = ['研发部', '产品部', '运营部', '市场部', '人力资源部', '财务部']

interface PermissionData {
  id?: string
  name: string
  unitName: string
  department: string
  position: string
  permissionType: string
  permissionItems: string[]
  status: string
}

const formData = reactive<PermissionData>({
  name: '',
  unitName: '',
  department: '',
  position: '',
  permissionType: '',
  permissionItems: [],
  status: 'pending'
})

const selectedItems = ref<string[]>([])

// 监听权限类型变化
watch(
  () => formData.permissionType,
  (newVal) => {
    if (newVal === '全部') {
      selectedItems.value = ['身份证号', '手机号', '银行卡号', '邮箱']
    } else if (newVal === '部分') {
      selectedItems.value = []
    }
  }
)

// 监听选中项目变化
watch(selectedItems, (newVal) => {
  formData.permissionItems = newVal
  if (newVal.length === 4) {
    formData.permissionType = '全部'
  } else if (newVal.length > 0) {
    formData.permissionType = '部分'
  }
})

const rules = {
  name: [{ required: true, message: '请输入姓名', trigger: 'blur' }],
  unitName: [{ required: true, message: '请输入单位名称', trigger: 'blur' }],
  department: [{ required: true, message: '请选择部门', trigger: 'change' }],
  position: [{ required: true, message: '请输入职位', trigger: 'blur' }],
  permissionType: [{ required: true, message: '请选择申请类型', trigger: 'change' }],
  permissionItems: [{ required: true, message: '请至少选择一个申请项目', trigger: 'change' }]
}

const show = (type: 'add' | 'edit', id?: string) => {
  dialogType.value = type
  if (type === 'add') {
    formData.name = ''
    formData.unitName = ''
    formData.department = ''
    formData.position = ''
    formData.permissionType = ''
    formData.permissionItems = []
    formData.status = 'pending'
  } else if (id) {
    const data = props.getData(id)
    if (data) {
      formData.id = data.id
      formData.name = data.name
      formData.unitName = data.unitName
      formData.department = data.department
      formData.position = data.position
      formData.permissionType = data.permissionType
      formData.permissionItems = data.permissionItems.split('、') as string[]
      formData.status = data.status
    }
  }
  emit('update:dialogVisible', true)
}

const submitForm = async () => {
  if (!formRef.value) return
  await formRef.value.validate((valid) => {
    if (valid) {
      const data = localStorage.getItem(STORAGE_KEY)
      const newData = {
        ...formData,
        permissionItems: formData.permissionItems.join('、')
      }
      if (data) {
        const allData = JSON.parse(data)
        if (dialogType.value === 'add') {
          allData.push({
            ...newData,
            id: Date.now().toString()
          })
        } else {
          const index = allData.findIndex((item: any) => item.id === formData.id)
          if (index !== -1) {
            allData[index] = newData
          }
        }
        localStorage.setItem(STORAGE_KEY, JSON.stringify(allData))
      } else {
        localStorage.setItem(
          STORAGE_KEY,
          JSON.stringify([
            {
              ...newData,
              id: Date.now().toString()
            }
          ])
        )
      }
      emit('refresh')
      emit('update:dialogVisible', false)
      ElMessage.success(dialogType.value === 'add' ? '新增成功' : '编辑成功')
    }
  })
}

defineExpose({
  show
})
</script>
