<template>
  <ContentWrap>
    <div style="width: 100%; height: 10px"></div>
    <div class="titleFlag">
      <div class="tf_icon"></div>
      <div class="tf_text">数据审批权限列表</div>
      <el-divider border-style="double" style="margin: 0 10px" />
    </div>

    <div class="flex gap-6">
      <!-- 左侧角色选择 -->
      <div class="w-64">
        <el-select
          v-model="selectedRole"
          placeholder="选择数据类型"
          class="w-full"
          @change="loadApprovalSettings(selectedRole)"
        >
          <el-option
            v-for="role in roles"
            :key="role.value"
            :label="role.label"
            :value="role.value"
          />
        </el-select>

        <div class="mt-4 p-4 bg-gray-50 rounded-lg">
          <h3 class="font-medium mb-2">审批规则设置</h3>
          <el-form :model="approvalRules" label-width="100px">
            <el-form-item label="时效提醒">
              <el-input-number
                v-model="approvalRules.reminderDays"
                :min="1"
                :max="7"
                class="w-24"
              />
              <span class="ml-2">天</span>
            </el-form-item>
            <el-form-item label="超时处理">
              <el-radio-group v-model="approvalRules.timeoutAction">
                <el-radio label="1">自动通过</el-radio>
                <el-radio label="2">自动驳回</el-radio>
              </el-radio-group>
            </el-form-item>
            <el-form-item label="允许转审">
              <el-switch v-model="approvalRules.enabled" />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 右侧审批流程 -->
      <div class="flex-1">
        <div class="flex items-center justify-between mb-4">
          <h3 class="font-medium">审批环节设置</h3>
          <el-button
            type="primary"
            class="!rounded-button whitespace-nowrap"
            @click="addApprovalStep"
          >
            <Icon icon="ep:plus" class="mr-1" />添加环节
          </el-button>
        </div>

        <div class="space-y-4">
          <div
            v-for="(step, index) in approvalSteps"
            :key="index"
            class="p-4 border rounded-lg bg-gray-50"
          >
            <div class="flex items-center justify-between mb-3">
              <div class="font-medium">环节 {{ index + 1 }}</div>
              <div class="flex items-center gap-2">
                <el-button
                  type="primary"
                  link
                  @click="moveStep(index, 'up')"
                  :disabled="index === 0"
                >
                  <Icon icon="ep:arrow-up" />
                </el-button>
                <el-button
                  type="primary"
                  link
                  @click="moveStep(index, 'down')"
                  :disabled="index === approvalSteps.length - 1"
                >
                  <Icon icon="ep:arrow-down" />
                </el-button>
                <el-button type="danger" link @click="removeStep(index)">
                  <Icon icon="ep:delete" />
                </el-button>
              </div>
            </div>
            <el-form :model="step" label-width="100px">
              <el-form-item label="审批单位">
                <el-input v-model="step.approvers" placeholder="请输入审批单位" />
                <!-- <el-select v-model="step.approvers" multiple placeholder="选择审批人" class="w-full">
                  <el-option v-for="user in users" :key="user.value" :label="user.label" :value="user.value" />
                </el-select> -->
              </el-form-item>
              <el-form-item label="是否会签">
                <el-switch v-model="step.needAllApprove" />
              </el-form-item>
            </el-form>
          </div>
        </div>

        <!-- 保存按钮 -->
        <div class="mt-4 text-right" v-if="saveButtonVisible">
          <el-button type="primary" @click="saveApprovalSettings">保存审批流程</el-button>
        </div>
      </div>
    </div>

    <Edit
      ref="editRef"
      v-model:dialog-visible.sync="dialogVisible"
      :get-data="getDataById"
      @refresh="getData"
    />
    <Detail
      ref="detailRef"
      v-model:dialog-visible.sync="dialogVisibleDetail"
      :get-data="getDataById"
    />
  </ContentWrap>
</template>

<script lang="ts" setup>
// 使用项目中的Icon组件
import { Icon } from '@/components/Icon'

import { ContentWrap } from '@/components/ContentWrap'
import Edit from './component/Edit.vue'
import Detail from './component/Detail.vue'
import {
  ElButton,
  ElMessage,
  ElDivider,
  ElForm,
  ElFormItem,
  ElSelect,
  ElOption,
  ElInputNumber,
  ElRadio,
  ElRadioGroup,
  ElSwitch,
  ElInput
} from 'element-plus'
import { ref, onMounted, watch } from 'vue'

const STORAGE_KEY = 'sensitive_data_permission'

// 数据类型角色列表
const roles = ref([
  { value: 'physical', label: '体检记录' },
  { value: 'diagnosis', label: '诊断记录' },
  { value: 'identification', label: '鉴定记录' },
  { value: 'medical', label: '医疗记录' },
  { value: 'rehabilitation', label: '职业病康复治疗记录' }
])

// 选中的数据类型
const selectedRole = ref('')

// 审批规则设置
const approvalRules = ref({
  reminderDays: 3, // 时效提醒天数
  timeoutAction: '1', // 超时处理：1-自动通过，2-自动驳回
  enabled: false // 允许转审
})

// 审批环节列表
const approvalSteps = ref([
  {
    approvers: '', // 审批单位
    needAllApprove: false // 是否会签
  }
])

// 获取数据 - 简化版
const getData = () => {
  // 从本地存储加载数据
  const data = localStorage.getItem(STORAGE_KEY)
  if (!data) {
    // 初始化数据
    const mockData = [
      {
        id: '1',
        name: '张明',
        unitName: '新疆维吾尔自治区人民政府办公厅',
        status: 'pending'
      }
    ]
    localStorage.setItem(STORAGE_KEY, JSON.stringify(mockData))
    total.value = mockData.length
  } else {
    // 设置总数
    const allData = JSON.parse(data)
    total.value = allData.length
  }
}

// 总数
const total = ref(0)

const editRef = ref<any>(null)
const detailRef = ref<any>(null)
const dialogVisible = ref(false)
const dialogVisibleDetail = ref(false)

// 编辑对话框函数
// const showEditDialog = (type: 'add' | 'edit' | 'view', id?: string) => {
//   if (editRef.value) {
//     editRef.value.show(type, id)
//   }
// }

// 详情对话框函数已移除

// 获取单个数据
const getDataById = (id: string) => {
  const data = localStorage.getItem(STORAGE_KEY)
  if (data) {
    const allData = JSON.parse(data)
    return allData.find((item: any) => item.id === id)
  }
  return null
}

// 添加审批环节
const addApprovalStep = () => {
  approvalSteps.value.push({
    approvers: '',
    needAllApprove: false
  })
}

// 移动审批环节
const moveStep = (index: number, direction: 'up' | 'down') => {
  if (direction === 'up' && index > 0) {
    // 向上移动
    const temp = approvalSteps.value[index]
    approvalSteps.value[index] = approvalSteps.value[index - 1]
    approvalSteps.value[index - 1] = temp
  } else if (direction === 'down' && index < approvalSteps.value.length - 1) {
    // 向下移动
    const temp = approvalSteps.value[index]
    approvalSteps.value[index] = approvalSteps.value[index + 1]
    approvalSteps.value[index + 1] = temp
  }
}

// 删除审批环节
const removeStep = (index: number) => {
  if (approvalSteps.value.length > 1) {
    approvalSteps.value.splice(index, 1)
  } else {
    ElMessage.warning('至少需要保留一个审批环节')
  }
}

// 保存审批流程设置
const saveApprovalSettings = () => {
  if (!selectedRole.value) {
    return ElMessage.error('请选择数据类型')
  }

  // 检查每个环节是否都选择了审批单位
  const invalidStep = approvalSteps.value.findIndex(
    (step: { approvers: string }) => !step.approvers
  )
  if (invalidStep !== -1) {
    return ElMessage.error(`环节 ${invalidStep + 1} 未选择审批单位`)
  }

  // 保存审批流程设置到本地存储
  const key = `approval_settings_${selectedRole.value}`
  const settings = {
    role: selectedRole.value,
    rules: approvalRules.value,
    steps: approvalSteps.value
  }
  localStorage.setItem(key, JSON.stringify(settings))
  ElMessage.success('审批流程设置保存成功')
}

// 加载审批流程设置
const loadApprovalSettings = (role: string) => {
  const key = `approval_settings_${role}`
  const settings = localStorage.getItem(key)
  if (settings) {
    const data = JSON.parse(settings)
    approvalRules.value = data.rules
    approvalSteps.value = data.steps
  } else {
    // 重置为默认值
    approvalRules.value = {
      reminderDays: 3,
      timeoutAction: '1',
      enabled: false
    }
    approvalSteps.value = [
      {
        approvers: '', // 审批单位
        needAllApprove: false
      }
    ]
  }
}

// 监听角色选择变化
watch(selectedRole, (newValue) => {
  if (newValue) {
    loadApprovalSettings(newValue)
  }
})

// 添加保存按钮
const saveButtonVisible = ref(true)

onMounted(() => {
  getData()
})
</script>

<style lang="less">
@import url('@/styles/institutionTitle.less');
</style>
