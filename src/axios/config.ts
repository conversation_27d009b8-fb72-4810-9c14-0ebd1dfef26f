import { AxiosResponse, InternalAxiosRequestConfig } from './types'
import { ElMessage } from 'element-plus'
import qs from 'qs'
import { TOKEN_EXPIRED, TRANSFORM_REQUEST_DATA } from '@/constants'
// import { TRANSFORM_REQUEST_DATA } from '@/constants'
import { useUserStoreWithOut } from '@/store/modules/user'
import { objToFormData } from '@/utils'

const defaultRequestInterceptors = (config: InternalAxiosRequestConfig) => {
  if (
    config.method === 'post' &&
    config.headers['Content-Type'] === 'application/x-www-form-urlencoded'
  ) {
    config.data = qs.stringify(config.data)
  } else if (
    TRANSFORM_REQUEST_DATA &&
    config.method === 'post' &&
    config.headers['Content-Type'] === 'multipart/form-data'
  ) {
    config.data = objToFormData(config.data)
  }
  if (config.method === 'get' && config.params) {
    let url = config.url as string
    url += '?'
    const keys = Object.keys(config.params)
    for (const key of keys) {
      if (config.params[key] !== void 0 && config.params[key] !== null) {
        url += `${key}=${encodeURIComponent(config.params[key])}&`
      }
    }
    url = url.substring(0, url.length - 1)
    config.params = {}
    config.url = url
  }
  return config
}

const defaultResponseInterceptors = (response: AxiosResponse) => {
  // console.log('🍊response', response)
  if (response?.config?.responseType === 'blob') {
    // 如果是文件流，直接过
    return response
  } else if (response.status === TOKEN_EXPIRED) {
    // 这一块实际上应该走不到，状态码401会走defaultResponseInterceptorsError
    ElMessage.error(response?.data?.message ?? '登录过期，请重新登录')
    // 重定向到 uncertified 页面
    const userStore = useUserStoreWithOut()
    userStore.logout('/uncertified')
  } else if (String(response.status).startsWith('2')) {
    return response.data
  } else {
    ElMessage.error(response?.data?.message)
  }
}

const defaultResponseInterceptorsError = (error: any) => {
  console.log('🍊err： ' + error, error.response.status)
  if (error.response.status === TOKEN_EXPIRED) {
    ElMessage.error(error.response?.data?.message ?? '登录过期，请重新登录')
    // 重定向到 uncertified 页面
    const userStore = useUserStoreWithOut()
    userStore.logout('/uncertified')
  }
  ElMessage.error(error.message)
  return Promise.reject(error)
}

export { defaultResponseInterceptorsError, defaultResponseInterceptors, defaultRequestInterceptors }
