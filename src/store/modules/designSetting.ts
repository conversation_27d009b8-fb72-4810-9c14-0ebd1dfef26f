import { defineStore } from 'pinia'
import { store } from '@/store'

// app theme preset color
const appThemeList: string[] = [
  '#2d8cf0',
  '#0960bd',
  '#0084f4',
  '#009688',
  '#536dfe',
  '#ff5c93',
  '#ee4f12',
  '#0096c7',
  '#9c27b0',
  '#ff9800',
  '#FF3D68',
  '#00C1D4',
  '#71EFA3',
  '#171010',
  '#78DEC7',
  '#1768AC',
  '#FB9300',
  '#FC5404'
]

const setting = {
  //深色主题
  darkTheme: false,
  //系统主题色
  appTheme: '#2d8cf0',
  //系统内置主题色列表
  appThemeList
}

interface DesignSettingState {
  //深色主题
  darkTheme: boolean
  //系统风格
  appTheme: string
  //系统内置风格
  appThemeList: string[]
}

export const useDesignSettingStore = defineStore({
  id: 'app-design-setting',
  state: (): DesignSettingState => ({
    darkTheme: setting.darkTheme,
    appTheme: setting.appTheme,
    appThemeList: setting.appThemeList
  }),
  getters: {
    getDarkTheme(): boolean {
      return this.darkTheme
    },
    getAppTheme(): string {
      return this.appTheme
    },
    getAppThemeList(): string[] {
      return this.appThemeList
    }
  },
  actions: {}
})

// Need to be used outside the setup
export function useDesignSetting() {
  return useDesignSettingStore(store)
}
