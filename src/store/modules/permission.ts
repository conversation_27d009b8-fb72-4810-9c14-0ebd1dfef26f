import { defineStore } from 'pinia'
import { asyncRouterMap, constantRouterMap } from '@/router'
import { dainamicGenerateRoutes, flatMultiLevelRoutes } from '@/utils/routerHelper'
import { store } from '../index'
import { cloneDeep } from 'lodash-es'
import { Layout } from '@/utils/routerHelper'

export interface PermissionState {
  routers: AppRouteRecordRaw[]
  addRouters: AppRouteRecordRaw[]
  isAddRouters: boolean
  menuTabRouters: AppRouteRecordRaw[]
  permissionList: string[]
}

export const usePermissionStore = defineStore('permission', {
  state: (): PermissionState => ({
    routers: [],
    addRouters: [],
    isAddRouters: false,
    menuTabRouters: [],

    permissionList: []
  }),
  getters: {
    getRouters(): AppRouteRecordRaw[] {
      return this.routers
    },
    getAddRouters(): AppRouteRecordRaw[] {
      return flatMultiLevelRoutes(cloneDeep(this.addRouters))
    },
    getIsAddRouters(): boolean {
      return this.isAddRouters
    },
    getMenuTabRouters(): AppRouteRecordRaw[] {
      return this.menuTabRouters
    },
    getPermissionList(): string[] {
      return this.permissionList
    }
  },
  actions: {
    setPermissionList(list: string[]): void {
      this.permissionList = list
    },
    generateRoutes(routers?: CustomRouteRaw[]): Promise<unknown> {
      return new Promise<void>((resolve) => {
        let routerMap: AppRouteRecordRaw[] = []
        // 后端过滤菜单
        routerMap = dainamicGenerateRoutes(cloneDeep(asyncRouterMap), routers as CustomRouteRaw[])
        console.log('routerMap===', routerMap)
        const staticRouterMap = [
          {
            path: '/',
            component: Layout,
            // redirect: routerMap[0].redirect || routerMap[0].path,
            name: 'Root',
            meta: {
              hidden: true
            }
          },
          // 动态路由，404一定要放到最后面
          {
            path: '/:path(.*)*',
            redirect: '/404',
            name: '404Page',
            meta: {
              hidden: true,
              breadcrumb: false
            }
          }
        ]
        // '/' 重定向到第一个菜单
        if (routerMap.length > 0) {
          staticRouterMap[0].redirect = (routerMap[0].redirect || routerMap[0].path) as string
        }
        this.addRouters = routerMap.concat(staticRouterMap)
        // 渲染菜单的所有路由
        this.routers = constantRouterMap.concat(routerMap)
        resolve()
      })
    },
    setIsAddRouters(state: boolean): void {
      this.isAddRouters = state
    },
    setMenuTabRouters(routers: AppRouteRecordRaw[]): void {
      this.menuTabRouters = routers
    }
  },
  persist: {
    paths: ['routers', 'addRouters', 'menuTabRouters', 'permissionList']
  }
})

export const usePermissionStoreWithOut = () => {
  return usePermissionStore(store)
}
