import request from '@/axios'

/**
 * 获取服务机构列表
 * @param params 查询参数
 * @returns 服务机构列表
 */
export const getServiceOrgList = (params?: any) => {
  return request.get({ url: '/serviceOrg', params })
}

/**
 * 获取服务机构详情
 * @param id 服务机构ID
 * @returns 服务机构详情
 */
export const getServiceOrgDetail = (id: string) => {
  return request.get({ url: `/serviceOrg/${id}` })
}

/**
 * 创建服务机构
 * @param data 服务机构数据
 * @returns 创建结果
 */
export const createServiceOrg = (data: any) => {
  return request.post({ url: '/serviceOrg', data })
}

/**
 * 更新服务机构
 * @param id 服务机构ID
 * @param data 更新数据
 * @returns 更新结果
 */
export const updateServiceOrg = (id: string, data: any) => {
  return request.put({ url: `/serviceOrg/${id}`, data })
}

/**
 * 删除服务机构
 * @param ids 服务机构ID数组
 * @returns 删除结果
 */
export const deleteServiceOrg = (ids: string[]) => {
  return request.delete({ url: '/serviceOrg', params: { ids } })
}
