export interface DepartmentItem {
  id: string
  departmentName: string
  children?: DepartmentItem[]
}

export interface DepartmentListResponse {
  list: DepartmentItem[]
}

export interface DepartmentUserParams {
  pageSize: number
  pageIndex: number
  id: string
  username?: string
  account?: string
}

export interface DepartmentUserItem {
  id: string
  username: string
  account: string
  phoneNum: string
  password: string
  createTime: string
  role: string
  department: DepartmentItem
}

export interface DepartmentUserResponse {
  list: DepartmentUserItem[]
  total: number
}

export interface DepartmentTypes {
  id: any
  institutionName: string
  administerAreaCode?: string | number
  administerAreaName?: string
  address: string
  state: number | string
}

export interface departType {
  institutionName: string
  administerArea: string
  address: string
  state: number
  createTime: string
}
