import request from '@/axios'
import type { UserType } from './types'

interface RoleParams {
  roleName: string
}

export const loginApi = (data: {
  username: string
  password: string
  // iv: string
}): Promise<IResponse<UserType>> => {
  return request.post({ url: '/auth/login', data })
}

export const loginOutApi = (): Promise<IResponse> => {
  return request.get({ url: '/auth/logout' })
}

export const testApi = (): Promise<IResponse<{ test: string }>> => {
  return request.get({ url: '/mock/user/test' })
}

export const authenticateApi = (params: {
  code: string
}): Promise<IResponse<{ user_name: string; token_expiration: string; token: string }>> => {
  return request.get({ url: '/auth/ssoLogin', params })
}

export const tokenExpiredApi = (): Promise<IResponse<{ test: string }>> => {
  return request.get({ url: '/mock/user/tokenExpired' })
}

export const getMenuApi = (): Promise<IResponse<CustomRouteRaw[]>> => {
  return request.get({ url: '/menu' })
}

export const getUserInfoApi = () => {
  return request.get({ url: '/sysUser/byToken' })
}

export const getTestRoleApi = (params: RoleParams): Promise<IResponse<string[]>> => {
  return request.get({ url: '/mock/role/list2', params })
}
