import request from '@/axios'

// 注册时获取用人单位下拉列表
export const getEmployerList = (params: any) => {
  return request.get({ url: '/customerOrg/byKeyword', params })
}

// 注册获取短信验证码
export const getVerifyCode = (params: any) => {
  return request.get({ url: `/captcha/register/${params.mobile}` })
}

// 注册
export const userRegister = (data: any) => {
  return request.post({ url: `/user`, data })
}

// 用人单位登录
export const userLogin = (data: any) => {
  return request.post({ url: `/login/byPassword`, data })
}
