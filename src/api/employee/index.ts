import request from '@/axios'

export const employeeRegisterlist = (params: any) => {
  return request.get({ url: `/employee/registerlist`, params })
}

export const getItemList = () => {
  return request.get({ url: `/employee/itemList` })
}

// 获取企业列表
export const getEnterpriseList = (params?: any) => {
  return request.get({ url: '/enterprise', params })
}

// 获取企业详情
export const getEnterpriseDetail = (id: string) => {
  return request.get({ url: `/enterprise/${id}` })
}

// 获取员工列表
export const getEmployeeList = (params?: any) => {
  return request.get({ url: '/employee', params })
}

// 获取员工详情
export const getEmployeeDetail = (id: string) => {
  return request.get({ url: `/employee/${id}` })
}

// 创建员工
export const createEmployee = (data: any) => {
  return request.post({ url: '/employee', data })
}

//#region infoSet
// 获取信息集列表
export const getInfoSetList = (params?: any) => {
  return request.get({ url: '/infoSet', params })
}

// 获取信息集详情
export const getInfoSetdetail = (id?: any) => {
  return request.get({ url: `/infoSet/${id}` })
}

// 创建信息集
export const createInfoSet = (data: any) => {
  return request.post({ url: '/infoSet', data })
}

// 更新信息集
export const updateInfoSet = ({ id, ...data }) => {
  return request.put({ url: `/infoSet/${id}`, data })
}

// 删除信息集
export const deleteInfoSet = (ids: number[]) => {
  return request.delete({ url: `/infoSet`, params: { ids } })
}
// #endregion

//#region employee-category
// 获取劳动者类别列表
export const getEmployeeCategoryList = (params?: any) => {
  return request.get({ url: '/employeeCategory', params })
}

// 获取劳动者类别详情
export const getEmployeeCategoryDetail = (id: string) => {
  return request.get({ url: `/employeeCategory/${id}` })
}

// 创建劳动者类别
export const createEmployeeCategory = (data: any) => {
  return request.post({ url: '/employeeCategory', data })
}

// 更新劳动者类别
export const updateEmployeeCategory = (id: string, data: any) => {
  return request.put({ url: `/employeeCategory/${id}`, data })
}

// 删除劳动者类别
export const deleteEmployeeCategory = (ids: number[]) => {
  return request.delete({ url: '/employeeCategory', params: { ids } })
}
// #endregion

// 获取员工职业史
export const getWorkHistory = (params: any) => {
  return request.get({ url: `/employee/workHistoryList`, params })
}
// 获取员工诊断记录
export const getDiagnosticRecordList = (params: any) => {
  return request.get({ url: `/employee/diagnosticRecordList`, params })
}
// 获取员工鉴定记录
export const getDeterminationRecordList = (params: any) => {
  return request.get({ url: `/employee/determinationRecordList`, params })
}
