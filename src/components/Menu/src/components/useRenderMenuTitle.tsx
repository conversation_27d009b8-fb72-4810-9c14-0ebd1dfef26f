import type { RouteMeta } from 'vue-router'
import { Icon } from '@/components/Icon'
import { useI18n } from '@/hooks/web/useI18n'

export const useRenderMenuTitle = () => {
  const renderMenuTitle = (meta: RouteMeta) => {
    const { t } = useI18n()
    const { title = 'Please set title', icon, menuLevel = 2 } = meta

    let titleClass = menuLevel === 1 ? 'menuLevel1' : 'menuLevel2'
    titleClass += ' v-menu__title overflow-hidden overflow-ellipsis whitespace-nowrap'
    const iconColor = menuLevel === 1 ? '#3f8df2' : ''

    return icon ? (
      <>
        <Icon icon={meta.icon} color={iconColor}></Icon>
        <span class={titleClass}>{t(title as string)}</span>
      </>
    ) : (
      <span class={titleClass}>{t(title as string)}</span>
    )
  }

  return {
    renderMenuTitle
  }
}
