FROM registry.duopu.cn/zyws/docker/nginx:stable-alpine
COPY ./dist/ /usr/share/nginx/html
COPY nginx.conf /etc/nginx/conf.d/default.conf
RUN mkdir -p /opt/log && chmod 777 -R /opt/log
EXPOSE 8080
ENTRYPOINT ["/bin/sh", "-c", \
  "echo \"window._env_ = { VITE_REDIRECT_401_PATH: '${VITE_REDIRECT_401_PATH}' };\" \
  > /usr/share/nginx/html/config.js && exec nginx -g 'daemon off;'"]                                                         
